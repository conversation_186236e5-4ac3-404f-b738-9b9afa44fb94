#!/bin/bash

# RecoverDev Release Build Script
# This script builds a signed and obfuscated release APK

echo "🚀 Starting RecoverDev Release Build..."

# Check if keystore exists
if [ ! -f "keystore.properties" ]; then
    echo "❌ Error: keystore.properties not found!"
    echo "Please create keystore.properties file with signing configuration."
    exit 1
fi

if [ ! -f "app/recoverdev-release-key.jks" ]; then
    echo "❌ Error: Keystore file not found!"
    echo "Please ensure app/recoverdev-release-key.jks exists."
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
./gradlew clean

# Build release APK
echo "🔨 Building release APK with obfuscation..."
./gradlew assembleRelease

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Release build completed successfully!"
    echo ""
    echo "📦 Generated files:"
    echo "   APK: app/build/outputs/apk/release/app-release.apk"
    echo "   Mapping: app/build/outputs/mapping/release/mapping.txt"
    echo ""
    echo "🔒 Security features enabled:"
    echo "   ✓ Code obfuscation with ProGuard"
    echo "   ✓ Resource shrinking"
    echo "   ✓ APK signing with release key"
    echo "   ✓ Debug symbols removed"
    echo "   ✓ Logging statements removed"
    echo ""
    echo "⚠️  Important: Keep the mapping.txt file for crash analysis!"
else
    echo "❌ Build failed! Check the error messages above."
    exit 1
fi