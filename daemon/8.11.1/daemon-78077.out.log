2025-08-27T10:20:05.262+0800 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonMain] Assuming the daemon was started with following jvm opts: [-Xmx2048m, -Dfile.encoding=UTF-8, -Duser.country=CN, -Duser.language=zh, -Duser.variant]
2025-08-27T10:20:05.581+0800 [INFO] [org.gradle.launcher.daemon.server.Daemon] start() called on daemon - DefaultDaemonContext[uid=863ad3f3-d776-4f1e-8cd5-e27f995dbc57,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.8/Contents/Home,javaVersion=17,javaVendor=JetBrains s.r.o.,daemonRegistryDir=/Volumes/MobileSSD/projects/local/RecoverDev/daemon,pid=78077,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=CN,-Duser.language=zh,-Duser.variant]
2025-08-27T10:20:05.758+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun3
2025-08-27T10:20:05.759+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.761+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:ce81:b1c:bd2c:69e%utun3
2025-08-27T10:20:05.762+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun2
2025-08-27T10:20:05.763+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.766+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:27c5:5f74:ca24:742b%utun2
2025-08-27T10:20:05.767+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun1
2025-08-27T10:20:05.768+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.768+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:e4d3:71d7:c676:d14d%utun1
2025-08-27T10:20:05.768+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface utun0
2025-08-27T10:20:05.769+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.769+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:8fa5:c63a:fa73:7b58%utun0
2025-08-27T10:20:05.769+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface llw0
2025-08-27T10:20:05.769+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.770+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:ccaa:8dff:fe3f:8b31%llw0
2025-08-27T10:20:05.771+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface awdl0
2025-08-27T10:20:05.772+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.773+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:ccaa:8dff:fe3f:8b31%awdl0
2025-08-27T10:20:05.773+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface bridge101
2025-08-27T10:20:05.774+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.774+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fdb2:2c26:f4e4:1:0:0:0:1%bridge101
2025-08-27T10:20:05.774+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:1498:77ff:fe08:7565%bridge101
2025-08-27T10:20:05.775+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /***********
2025-08-27T10:20:05.775+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface bridge100
2025-08-27T10:20:05.776+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.776+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fdb2:2c26:f4e4:0:0:0:0:1%bridge100
2025-08-27T10:20:05.776+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:1498:77ff:fe08:7564%bridge100
2025-08-27T10:20:05.777+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /***********
2025-08-27T10:20:05.777+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface en1
2025-08-27T10:20:05.777+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? false
2025-08-27T10:20:05.778+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /fe80:0:0:0:829:2bfe:f351:5281%en1
2025-08-27T10:20:05.778+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding remote address /************
2025-08-27T10:20:05.778+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding IP addresses for network interface lo0
2025-08-27T10:20:05.778+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Is this a loopback interface? true
2025-08-27T10:20:05.779+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Ignoring remote address on loopback interface /fe80:0:0:0:0:0:0:1%lo0
2025-08-27T10:20:05.781+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /0:0:0:0:0:0:0:1%lo0
2025-08-27T10:20:05.781+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.InetAddresses] Adding loopback address /127.0.0.1
2025-08-27T10:20:05.796+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Listening on [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]].
2025-08-27T10:20:05.837+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] Daemon starting at: Wed Aug 27 10:20:05 CST 2025, with address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:20:05.841+0800 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertising the daemon address to the clients: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:20:05.842+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Advertised daemon context: DefaultDaemonContext[uid=863ad3f3-d776-4f1e-8cd5-e27f995dbc57,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.8/Contents/Home,javaVersion=17,javaVendor=JetBrains s.r.o.,daemonRegistryDir=/Volumes/MobileSSD/projects/local/RecoverDev/daemon,pid=78077,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=CN,-Duser.language=zh,-Duser.variant]
2025-08-27T10:20:05.846+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]], context: DefaultDaemonContext[uid=863ad3f3-d776-4f1e-8cd5-e27f995dbc57,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.8/Contents/Home,javaVersion=17,javaVendor=JetBrains s.r.o.,daemonRegistryDir=/Volumes/MobileSSD/projects/local/RecoverDev/daemon,pid=78077,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=CN,-Duser.language=zh,-Duser.variant]
2025-08-27T10:20:05.890+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:20:05.895+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:20:05.904+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:20:05.905+0800 [LIFECYCLE] [org.gradle.launcher.daemon.server.Daemon] Daemon server started.
2025-08-27T10:20:05.908+0800 [DEBUG] [org.gradle.launcher.daemon.bootstrap.DaemonStartupCommunication] Completed writing the daemon greeting. Closing streams...
2025-08-27T10:20:05.924+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stopOnExpiration() called on daemon
2025-08-27T10:20:05.928+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] awaitExpiration() called on daemon
2025-08-27T10:20:05.928+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-08-27T10:20:05.937+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:62713 to /127.0.0.1:62712.
2025-08-27T10:20:06.009+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: received class org.gradle.launcher.daemon.protocol.Build
2025-08-27T10:20:06.009+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 21: Received non-IO message from client: Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}
2025-08-27T10:20:06.011+0800 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}.
2025-08-27T10:20:06.011+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev} with connection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62713.
2025-08-27T10:20:06.018+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: started DaemonCommandExecution[command = Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62713] after 0.006666666666666667 minutes of idle
2025-08-27T10:20:06.019+0800 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as busy, address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:20:06.020+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:20:06.021+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:20:06.022+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:20:06.026+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:20:06.027+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2025-08-27T10:20:06.027+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-08-27T10:20:06.029+0800 [INFO] [org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy] Daemon is about to start building Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}. Dispatching build started information...
2025-08-27T10:20:06.029+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 23: dispatching org.gradle.launcher.daemon.protocol.BuildStarted@1ba2b28a
2025-08-27T10:20:06.034+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment] Configuring env variables: [PATH, __CFBundleIdentifier, SHELL, HOMEBREW_BOTTLE_DOMAIN, HOMEBREW_CELLAR, OLDPWD, USER, HOMEBREW_PREFIX, COMMAND_MODE, TMPDIR, SSH_AUTH_SOCK, XPC_FLAGS, ANTHROPIC_AUTH_TOKEN, __CF_USER_TEXT_ENCODING, ANTHROPIC_BASE_URL, LOGNAME, HOMEBREW_REPOSITORY, LC_CTYPE, XPC_SERVICE_NAME, INFOPATH, HOME]
2025-08-27T10:20:06.039+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.LogToClient] About to start relaying all logs to the client via the connection.
2025-08-27T10:20:06.040+0800 [INFO] [org.gradle.launcher.daemon.server.exec.LogToClient] The client will now receive all logging from the daemon (pid: 78077). The daemon log file: /Volumes/MobileSSD/projects/local/RecoverDev/daemon/8.11.1/daemon-78077.out.log
2025-08-27T10:20:06.043+0800 [INFO] [org.gradle.launcher.daemon.server.exec.LogAndCheckHealth] Starting build in new daemon [memory: 2 GiB]
2025-08-27T10:20:06.046+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has started executing the build.
2025-08-27T10:20:06.047+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] Executing build with daemon context: DefaultDaemonContext[uid=863ad3f3-d776-4f1e-8cd5-e27f995dbc57,javaHome=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.8/Contents/Home,javaVersion=17,javaVendor=JetBrains s.r.o.,daemonRegistryDir=/Volumes/MobileSSD/projects/local/RecoverDev/daemon,pid=78077,idleTimeout=10800000,priority=NORMAL,applyInstrumentationAgent=true,nativeServicesMode=ENABLED,daemonOpts=-Xmx2048m,-Dfile.encoding=UTF-8,-Duser.country=CN,-Duser.language=zh,-Duser.variant]
w: file:///Volumes/MobileSSD/projects/local/RecoverDev/app/src/main/java/com/example/recoverdev/recovery/FileRecoveryEngine.kt:1037:76 'static field ACTION_MEDIA_SCANNER_SCAN_FILE: String' is deprecated. Deprecated in Java.
w: file:///Volumes/MobileSSD/projects/local/RecoverDev/app/src/main/java/com/example/recoverdev/ui/screen/ScanningScreen.kt:119:13 'fun LinearProgressIndicator(progress: Float, modifier: Modifier = ..., color: Color = ..., trackColor: Color = ..., strokeCap: StrokeCap = ...): Unit' is deprecated. Use the overload that takes `progress` as a lambda.

BUILD SUCCESSFUL in 1m 46s
32 actionable tasks: 9 executed, 23 up-to-date
2025-08-27T10:21:48.435+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.ExecuteBuild] The daemon has finished executing the build.
2025-08-27T10:21:48.455+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-08-27T10:21:48.455+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: finished waiting for DaemonCommandExecution[command = Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62713]. Result org.gradle.launcher.daemon.server.DaemonStateCoordinator@3ed6f70c with state Busy
2025-08-27T10:21:48.456+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Command execution: completed DaemonCommandExecution[command = Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}, connection = DefaultDaemonConnection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62713]
2025-08-27T10:21:48.456+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] resetting idle timer
2025-08-27T10:21:48.456+0800 [INFO] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Marking the daemon as idle, address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:21:48.457+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Marking busy by address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:21:48.457+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:21:48.458+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:21:48.473+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:21:48.474+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.ReturnResult] Daemon is dispatching the build result: Success[value=org.gradle.launcher.exec.BuildActionResult@c3d0aec]
2025-08-27T10:21:48.474+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: dispatching Success[value=org.gradle.launcher.exec.BuildActionResult@c3d0aec]
2025-08-27T10:21:48.474+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon is running. Sleeping until state changes.
2025-08-27T10:21:48.483+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: Build{id=a593296b-45d9-475f-b0d6-86deb5c5ad66, currentDir=/Volumes/MobileSSD/projects/local/RecoverDev}
2025-08-27T10:21:48.483+0800 [DEBUG] [org.gradle.launcher.daemon.server.exec.CleanUpVirtualFileSystemAfterBuild] Cleaning virtual file system after build finished
2025-08-27T10:21:48.884+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: received class org.gradle.launcher.daemon.protocol.CloseInput
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 21: Received IO message from client: org.gradle.launcher.daemon.protocol.CloseInput@3c98108
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: received class org.gradle.launcher.daemon.protocol.Finished
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 21: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@65671d2d
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 21: received null
2025-08-27T10:21:48.885+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 21: Received end-of-input from client.
2025-08-27T10:21:48.892+0800 [WARN] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Timed out waiting for finished message from client socket connection from /127.0.0.1:62712 to /127.0.0.1:62713. Discarding connection.
2025-08-27T10:21:48.893+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: stopping connection
2025-08-27T10:21:48.901+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: stopping connection
2025-08-27T10:21:52.802+0800 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(2)-127.0.0.1: (port 62723) connection closed
2025-08-27T10:21:52.819+0800 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(2)-127.0.0.1: close connection, socket: Socket[addr=/127.0.0.1,port=62811,localport=62723]
2025-08-27T10:21:52.832+0800 [DEBUG] [sun.rmi.transport.tcp] RMI TCP Connection(2)-127.0.0.1: socket close: Socket[addr=/127.0.0.1,port=62811,localport=62723]
2025-08-27T10:21:54.874+0800 [DEBUG] [sun.rmi.transport.tcp] RMI Scheduler(0): close connection, socket: Socket[addr=localhost/127.0.0.1,port=17793,localport=62722]
2025-08-27T10:21:55.947+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2025-08-27T10:21:55.953+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:21:55.957+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:21:55.971+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2025-08-27T10:21:55.975+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:21:55.976+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:05.929+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2025-08-27T10:22:05.932+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:05.933+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:05.937+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2025-08-27T10:22:05.938+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:05.938+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:05.939+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire shared lock on daemon addresses registry.
2025-08-27T10:22:05.939+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:05.939+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:11.006+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:62901 to /127.0.0.1:62712.
2025-08-27T10:22:11.011+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:62902 to /127.0.0.1:62712.
2025-08-27T10:22:11.052+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 161: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2025-08-27T10:22:11.053+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 162: received class org.gradle.launcher.daemon.protocol.StopWhenIdle
2025-08-27T10:22:11.053+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 162: Received non-IO message from client: StopWhenIdle[id=8e651bd5-ae02-411b-9dc2-80049f89f3ac]
2025-08-27T10:22:11.053+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 161: Received non-IO message from client: StopWhenIdle[id=aadbf5cf-e674-4891-9603-d8606d27cfb8]
2025-08-27T10:22:11.057+0800 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=8e651bd5-ae02-411b-9dc2-80049f89f3ac].
2025-08-27T10:22:11.058+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=8e651bd5-ae02-411b-9dc2-80049f89f3ac] with connection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62902.
2025-08-27T10:22:11.060+0800 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: StopWhenIdle[id=aadbf5cf-e674-4891-9603-d8606d27cfb8].
2025-08-27T10:22:11.061+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: StopWhenIdle[id=aadbf5cf-e674-4891-9603-d8606d27cfb8] with connection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62901.
2025-08-27T10:22:11.067+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2025-08-27T10:22:11.105+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1756261331071
2025-08-27T10:22:11.118+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:22:11.132+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:11.181+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:11.182+0800 [LIFECYCLE] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Daemon will be stopped at the end of the build stop command received
2025-08-27T10:22:11.182+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] Marking daemon stopped due to stop command received. The daemon is not running a build
2025-08-27T10:22:11.182+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 160: dispatching Success[value=null]
2025-08-27T10:22:11.183+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=8e651bd5-ae02-411b-9dc2-80049f89f3ac]
2025-08-27T10:22:11.185+0800 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51029 confirmed unlock request for lock with id 324976899132794112.
2025-08-27T10:22:11.185+0800 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] Gradle process at port 51029 confirmed unlock request for lock with id 324976899132794112.
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Storing daemon stop event: stop command received
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Storing daemon stop event with timestamp 1756261331187
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:11.187+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: dispatching Success[value=null]
2025-08-27T10:22:11.188+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: StopWhenIdle[id=aadbf5cf-e674-4891-9603-d8606d27cfb8]
2025-08-27T10:22:11.190+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonStateCoordinator] daemon has stopped.
2025-08-27T10:22:11.191+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 162: received class org.gradle.launcher.daemon.protocol.Finished
2025-08-27T10:22:11.192+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 162: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@15938717
2025-08-27T10:22:11.192+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2025-08-27T10:22:11.192+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 162: received null
2025-08-27T10:22:11.192+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 162: Received end-of-input from client.
2025-08-27T10:22:11.197+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 161: received class org.gradle.launcher.daemon.protocol.Finished
2025-08-27T10:22:11.197+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 161: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@2febc184
2025-08-27T10:22:11.197+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2025-08-27T10:22:11.197+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 161: received null
2025-08-27T10:22:11.198+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 161: Received end-of-input from client.
2025-08-27T10:22:11.198+0800 [WARN] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Timed out waiting for finished message from client socket connection from /127.0.0.1:62712 to /127.0.0.1:62902. Discarding connection.
2025-08-27T10:22:11.198+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 160: stopping connection
2025-08-27T10:22:11.197+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.TcpIncomingConnector] Accepted connection from /127.0.0.1:62903 to /127.0.0.1:62712.
2025-08-27T10:22:11.200+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2025-08-27T10:22:11.200+0800 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2025-08-27T10:22:11.202+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:22:11.202+0800 [WARN] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Timed out waiting for finished message from client socket connection from /127.0.0.1:62712 to /127.0.0.1:62901. Discarding connection.
2025-08-27T10:22:11.203+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: stopping connection
2025-08-27T10:22:11.204+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:22:11.208+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 20: stopping connection
2025-08-27T10:22:11.209+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 160: stopping connection
2025-08-27T10:22:11.213+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:22:11.213+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:11.214+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:11.214+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2025-08-27T10:22:11.232+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 164: received class org.gradle.launcher.daemon.protocol.ReportStatus
2025-08-27T10:22:11.232+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 164: Received non-IO message from client: ReportStatus[id=0e8fcceb-8fe0-443f-ae74-a28d1714ef30]
2025-08-27T10:22:11.233+0800 [INFO] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Received command: ReportStatus[id=0e8fcceb-8fe0-443f-ae74-a28d1714ef30].
2025-08-27T10:22:11.238+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Starting executing command: ReportStatus[id=0e8fcceb-8fe0-443f-ae74-a28d1714ef30] with connection: socket connection from /127.0.0.1:62712 to /127.0.0.1:62903.
2025-08-27T10:22:11.260+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 163: dispatching Success[value=org.gradle.launcher.daemon.protocol.Status@1f58860c]
2025-08-27T10:22:11.265+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 164: received class org.gradle.launcher.daemon.protocol.Finished
2025-08-27T10:22:11.267+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 164: Received non-IO message from client: org.gradle.launcher.daemon.protocol.Finished@38c6b154
2025-08-27T10:22:11.269+0800 [DEBUG] [org.gradle.internal.remote.internal.inet.SocketConnection] Discarding EOFException: java.io.EOFException
2025-08-27T10:22:11.274+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 164: received null
2025-08-27T10:22:11.275+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultDaemonConnection] thread 164: Received end-of-input from client.
2025-08-27T10:22:11.266+0800 [DEBUG] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Finishing executing command: ReportStatus[id=0e8fcceb-8fe0-443f-ae74-a28d1714ef30]
2025-08-27T10:22:11.276+0800 [WARN] [org.gradle.launcher.daemon.server.DefaultIncomingConnectionHandler] Timed out waiting for finished message from client socket connection from /127.0.0.1:62712 to /127.0.0.1:62903. Discarding connection.
2025-08-27T10:22:11.276+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 163: stopping connection
2025-08-27T10:22:11.289+0800 [DEBUG] [org.gradle.launcher.daemon.server.SynchronizedDispatchConnection] thread 163: stopping connection
2025-08-27T10:22:11.322+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] jars (/Volumes/MobileSSD/projects/local/RecoverDev/caches/jars-9) has last been fully cleaned up 3 hours ago
2025-08-27T10:22:11.323+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Skipping cleanup for jars (/Volumes/MobileSSD/projects/local/RecoverDev/caches/jars-9) as it is not yet due
2025-08-27T10:22:11.323+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCoordinator] Cache jars (/Volumes/MobileSSD/projects/local/RecoverDev/caches/jars-9) was closed 0 times.
2025-08-27T10:22:11.332+0800 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file content cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileContent)
2025-08-27T10:22:11.335+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file content cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileContent).
2025-08-27T10:22:11.336+0800 [DEBUG] [org.gradle.internal.watch.vfs.impl.WatchingVirtualFileSystem] Closing VFS, dropping state
2025-08-27T10:22:11.347+0800 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for Generated Gradle JARs cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/generated-gradle-jars)
2025-08-27T10:22:11.347+0800 [DEBUG] [org.gradle.internal.watch.registry.impl.DefaultFileWatcherRegistry] Finished listening to file system change events
2025-08-27T10:22:11.348+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on Generated Gradle JARs cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/generated-gradle-jars).
2025-08-27T10:22:11.349+0800 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for artifact cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2)
2025-08-27T10:22:11.351+0800 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-artifact.bin (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2/metadata-2.107/module-artifact.bin)
2025-08-27T10:22:11.355+0800 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache module-metadata.bin (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2/metadata-2.107/module-metadata.bin)
2025-08-27T10:22:11.356+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on artifact cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2).
2025-08-27T10:22:11.357+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] artifact cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2) has last been fully cleaned up 3 hours ago
2025-08-27T10:22:11.357+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Skipping cleanup for artifact cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/modules-2) as it is not yet due
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] groovy-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/groovy-dsl) has last been fully cleaned up 3 hours ago
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Skipping cleanup for groovy-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/groovy-dsl) as it is not yet due
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCoordinator] Cache groovy-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/groovy-dsl) was closed 0 times.
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Artifact transforms cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/transforms) has last been fully cleaned up 3 hours ago
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Skipping cleanup for Artifact transforms cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/transforms) as it is not yet due
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCoordinator] Cache Artifact transforms cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/transforms) was closed 0 times.
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] kotlin-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/kotlin-dsl) has last been fully cleaned up 3 hours ago
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCleanupExecutor] Skipping cleanup for kotlin-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/kotlin-dsl) as it is not yet due
2025-08-27T10:22:11.358+0800 [DEBUG] [org.gradle.cache.internal.DefaultCacheCoordinator] Cache kotlin-dsl (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/kotlin-dsl) was closed 0 times.
2025-08-27T10:22:11.372+0800 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for journal cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/journal-1)
2025-08-27T10:22:11.373+0800 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache file-access.bin (/Volumes/MobileSSD/projects/local/RecoverDev/caches/journal-1/file-access.bin)
2025-08-27T10:22:11.373+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on journal cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/journal-1).
2025-08-27T10:22:11.376+0800 [DEBUG] [org.gradle.cache.internal.LockOnDemandCrossProcessCacheAccess] Releasing file lock for file hash cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileHashes)
2025-08-27T10:22:11.376+0800 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache fileHashes.bin (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileHashes/fileHashes.bin)
2025-08-27T10:22:11.381+0800 [DEBUG] [org.gradle.cache.internal.btree.BTreePersistentIndexedCache] Closing cache resourceHashesCache.bin (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileHashes/resourceHashesCache.bin)
2025-08-27T10:22:11.382+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on file hash cache (/Volumes/MobileSSD/projects/local/RecoverDev/caches/8.11.1/fileHashes).
2025-08-27T10:22:11.388+0800 [DEBUG] [org.gradle.internal.operations.DefaultBuildOperationRunner] Build operation 'Deleting unused version-specific caches in /Volumes/MobileSSD/projects/local/RecoverDev/caches' started
2025-08-27T10:22:11.390+0800 [DEBUG] [org.gradle.internal.operations.DefaultBuildOperationRunner] Completing Build operation 'Deleting unused version-specific caches in /Volumes/MobileSSD/projects/local/RecoverDev/caches'
2025-08-27T10:22:11.390+0800 [DEBUG] [org.gradle.internal.operations.DefaultBuildOperationRunner] Build operation 'Deleting unused version-specific caches in /Volumes/MobileSSD/projects/local/RecoverDev/caches' completed
2025-08-27T10:22:11.395+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] stop() called on daemon
2025-08-27T10:22:11.395+0800 [INFO] [org.gradle.launcher.daemon.server.Daemon] Stop requested. Daemon is removing its presence from the registry...
2025-08-27T10:22:11.395+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Removing our presence to clients, eg. removing this address from the registry: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:22:11.395+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:22:11.395+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Waiting to acquire exclusive lock on daemon addresses registry.
2025-08-27T10:22:11.396+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Lock acquired on daemon addresses registry.
2025-08-27T10:22:11.397+0800 [DEBUG] [org.gradle.cache.internal.DefaultFileLockManager] Releasing lock on daemon addresses registry.
2025-08-27T10:22:11.397+0800 [DEBUG] [org.gradle.launcher.daemon.server.DaemonRegistryUpdater] Address removed from registry.
2025-08-27T10:22:11.398+0800 [DEBUG] [org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler] File lock listener thread completed.
2025-08-27T10:22:11.411+0800 [DEBUG] [org.gradle.launcher.daemon.registry.PersistentDaemonRegistry] Removing daemon address: [0f8f3f16-1c58-4de7-b7ae-3e68522972a8 port:62712, addresses:[localhost/127.0.0.1]]
2025-08-27T10:22:11.412+0800 [DEBUG] [org.gradle.launcher.daemon.server.Daemon] VM shutdown hook was unable to remove the daemon address from the registry. It will be cleaned up later.
java.lang.IllegalStateException: Cannot start managing file contention because this handler has been closed.
	at org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler.assertNotStopped(DefaultFileLockContentionHandler.java:217)
	at org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler.getCommunicator(DefaultFileLockContentionHandler.java:260)
	at org.gradle.cache.internal.locklistener.DefaultFileLockContentionHandler.reservePort(DefaultFileLockContentionHandler.java:254)
	at org.gradle.cache.internal.DefaultFileLockManager.lock(DefaultFileLockManager.java:124)
	at org.gradle.cache.internal.DefaultFileLockManager.lock(DefaultFileLockManager.java:106)
	at org.gradle.cache.internal.DefaultFileLockManager.lock(DefaultFileLockManager.java:101)
	at org.gradle.cache.internal.OnDemandFileAccess.updateFile(OnDemandFileAccess.java:51)
	at org.gradle.cache.internal.FileBackedObjectHolder.update(FileBackedObjectHolder.java:77)
	at org.gradle.cache.internal.FileIntegrityViolationSuppressingObjectHolderDecorator.lambda$update$0(FileIntegrityViolationSuppressingObjectHolderDecorator.java:48)
	at org.gradle.cache.internal.FileIntegrityViolationSuppressingObjectHolderDecorator.doUpdate(FileIntegrityViolationSuppressingObjectHolderDecorator.java:58)
	at org.gradle.cache.internal.FileIntegrityViolationSuppressingObjectHolderDecorator.update(FileIntegrityViolationSuppressingObjectHolderDecorator.java:48)
	at org.gradle.launcher.daemon.registry.PersistentDaemonRegistry.remove(PersistentDaemonRegistry.java:133)
	at org.gradle.launcher.daemon.server.Daemon$1.run(Daemon.java:133)
	at java.base/java.lang.Thread.run(Thread.java:833)
Daemon vm is shutting down... The daemon has exited normally or was terminated in response to a user interrupt.
