<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.test.espresso</groupId>
  <artifactId>espresso-core</artifactId>
  <version>3.6.1</version>
  <packaging>aar</packaging>
  <name>AndroidX Test Library</name>
  <description>The AndroidX Test Library provides an extensive framework for testing Android apps</description>
  <url>https://developer.android.com/testing</url>
  <inceptionYear>2015</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation</artifactId>
      <version>1.7.0-beta01</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.concurrent</groupId>
      <artifactId>concurrent-futures</artifactId>
      <version>1.1.0</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.test.espresso</groupId>
      <artifactId>espresso-idling-resource</artifactId>
      <version>3.6.1</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.test.services</groupId>
      <artifactId>storage</artifactId>
      <version>1.5.0</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.test</groupId>
      <artifactId>core</artifactId>
      <version>1.6.1</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.test</groupId>
      <artifactId>monitor</artifactId>
      <version>1.7.1</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>androidx.test</groupId>
      <artifactId>runner</artifactId>
      <version>1.6.1</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
      <version>3.0.2</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
      <version>2.18.0</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>listenablefuture</artifactId>
      <version>1.0</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.13.2</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-core</artifactId>
      <version>1.3</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>org.hamcrest</groupId>
      <artifactId>hamcrest-library</artifactId>
      <version>1.3</version>
      <scope>compile</scope>

    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.8.20</version>
      <scope>compile</scope>

    </dependency>
  </dependencies>
</project>
