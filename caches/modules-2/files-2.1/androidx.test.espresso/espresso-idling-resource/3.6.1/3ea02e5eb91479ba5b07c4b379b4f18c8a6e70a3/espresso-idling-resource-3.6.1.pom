<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.test.espresso</groupId>
  <artifactId>espresso-idling-resource</artifactId>
  <version>3.6.1</version>
  <packaging>aar</packaging>
  <name>AndroidX Test Library</name>
  <description>The AndroidX Test Library provides an extensive framework for testing Android apps</description>
  <url>https://developer.android.com/testing</url>
  <inceptionYear>2015</inceptionYear>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <dependencies>

  </dependencies>
</project>
