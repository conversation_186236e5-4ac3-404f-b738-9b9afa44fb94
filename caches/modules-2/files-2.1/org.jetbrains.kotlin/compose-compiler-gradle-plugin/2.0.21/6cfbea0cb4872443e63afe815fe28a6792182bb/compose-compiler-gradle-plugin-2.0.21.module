{"formatVersion": "1.1", "component": {"group": "org.jetbrains.kotlin", "module": "compose-compiler-gradle-plugin", "version": "2.0.21", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.8"}}, "variants": [{"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}]}, {"name": "runtimeElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21.jar", "url": "compose-compiler-gradle-plugin-2.0.21.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}]}, {"name": "apiElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21.jar", "url": "compose-compiler-gradle-plugin-2.0.21.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}]}, {"name": "gradle70JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle70-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle70-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle70-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle70-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle70"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle70.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle70.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle70RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle70"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle70.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle70.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle71-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle71-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle71-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle71-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle71"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle71.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle71.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle71RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle71"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle71.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle71.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle74-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle74-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle74-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle74-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle74"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle74.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle74.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle74RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle74"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle74.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle74.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle75-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle75-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle75-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle75-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle75"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle75.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle75.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle75RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle75"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle75.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle75.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle76-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle76-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle76-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle76-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle76"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle76.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle76.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle76RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle76"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle76.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle76.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle80-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle80-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle80-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle80-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle80"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle80.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle80.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle80RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle80"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle80.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle80.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle81-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle81-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle81-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle81-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle81"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle81.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle81.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle81RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle81"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle81.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle81.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle82-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle82-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle82-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle82-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle82"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle82.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle82.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle82RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle82"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle82.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle82.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle85-javadoc.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle85-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle85-sources.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle85-sources.jar", "size": 10922, "sha512": "4511dda829e9047b37513bf8d0c6bc79938d84c099ff67de65b0fd0cd4e3c47d28293ef099cf7f4d5eb57e7ab040c726d85e26cb32e5dc3a8cc8404bdd5ae451", "sha256": "03ddc5ea517672bed4e0c3c9e75486ff8caaedba29222f078b2438e949a1fa4a", "sha1": "29db7eb251e1661a45f57bbf0990d20b29aa6d6c", "md5": "ede0f0e9eefc7ef1f57d6fa2e330c60e"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle85"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle85.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle85.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}, {"name": "gradle85RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle85"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}], "files": [{"name": "compose-compiler-gradle-plugin-2.0.21-gradle85.jar", "url": "compose-compiler-gradle-plugin-2.0.21-gradle85.jar", "size": 41371, "sha512": "bc25f161366be75fac237aa3df0f383c9e907a7b0d5b1c7a4a0573310b4332b616ff3c0c71579510fd9f94a9ea54385c7c39442cfe0ebf01a0d30c746934bf84", "sha256": "68d4c74f6da0d5e12bb7255b8d6ddbd8868f0a249d4dfdf4ece70f6d3fbf5d03", "sha1": "b6c8e44232cfb9853d27309ba04eaa09e8e1df38", "md5": "e7a0127ca8eda68d3e1771d6c74a4aa0"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "compose-compiler-gradle-plugin", "version": "2.0.21"}]}]}