{"formatVersion": "1.1", "component": {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-model", "version": "2.0.21", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.8"}}, "variants": [{"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}]}, {"name": "runtimeElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21.jar", "url": "kotlin-gradle-plugin-model-2.0.21.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}]}, {"name": "apiElementsWithFixedAttribute", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugins-bom", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21.jar", "url": "kotlin-gradle-plugin-model-2.0.21.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}]}, {"name": "gradle70JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle70-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle70-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle70", "version": "2.0.21"}]}, {"name": "gradle70SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle70-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle70-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle70", "version": "2.0.21"}]}, {"name": "gradle70ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle70"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle70.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle70.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle70", "version": "2.0.21"}]}, {"name": "gradle70RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle70"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle70.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle70.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle70", "version": "2.0.21"}]}, {"name": "gradle71JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle71-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle71-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle71", "version": "2.0.21"}]}, {"name": "gradle71SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle71-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle71-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle71", "version": "2.0.21"}]}, {"name": "gradle71ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle71"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle71.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle71.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle71", "version": "2.0.21"}]}, {"name": "gradle71RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle71"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle71.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle71.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle71", "version": "2.0.21"}]}, {"name": "gradle74JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle74-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle74-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle74", "version": "2.0.21"}]}, {"name": "gradle74SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle74-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle74-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle74", "version": "2.0.21"}]}, {"name": "gradle74ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle74"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle74.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle74.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle74", "version": "2.0.21"}]}, {"name": "gradle74RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.4", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle74"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle74.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle74.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle74", "version": "2.0.21"}]}, {"name": "gradle75JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle75-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle75-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle75", "version": "2.0.21"}]}, {"name": "gradle75SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle75-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle75-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle75", "version": "2.0.21"}]}, {"name": "gradle75ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle75"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle75.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle75.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle75", "version": "2.0.21"}]}, {"name": "gradle75RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle75"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle75.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle75.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle75", "version": "2.0.21"}]}, {"name": "gradle76JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle76-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle76-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle76", "version": "2.0.21"}]}, {"name": "gradle76SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle76-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle76-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle76", "version": "2.0.21"}]}, {"name": "gradle76ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle76"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle76.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle76.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle76", "version": "2.0.21"}]}, {"name": "gradle76RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "7.6", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle76"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle76.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle76.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle76", "version": "2.0.21"}]}, {"name": "gradle80JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle80-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle80-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle80", "version": "2.0.21"}]}, {"name": "gradle80SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle80-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle80-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle80", "version": "2.0.21"}]}, {"name": "gradle80ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle80"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle80.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle80.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle80", "version": "2.0.21"}]}, {"name": "gradle80RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.0", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle80"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle80.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle80.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle80", "version": "2.0.21"}]}, {"name": "gradle81JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle81-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle81-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle81", "version": "2.0.21"}]}, {"name": "gradle81SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle81-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle81-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle81", "version": "2.0.21"}]}, {"name": "gradle81ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle81"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle81.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle81.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle81", "version": "2.0.21"}]}, {"name": "gradle81RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.1", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle81"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle81.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle81.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle81", "version": "2.0.21"}]}, {"name": "gradle82JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle82-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle82-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle82", "version": "2.0.21"}]}, {"name": "gradle82SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle82-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle82-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle82", "version": "2.0.21"}]}, {"name": "gradle82ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle82"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle82.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle82.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle82", "version": "2.0.21"}]}, {"name": "gradle82RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.2", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle82"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle82.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle82.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle82", "version": "2.0.21"}]}, {"name": "gradle85JavadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle85-javadoc.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle85-javadoc.jar", "size": 261, "sha512": "1487e5a20c9e4d74f298e07c42e96a61be11b7a768c3fbc199ee138cd68e1fc2267d1cfe3f11f288acc05df755315344d052cc5f6f751f126ab4fe1caa5125ea", "sha256": "c6deada2fac53b8ea6523dbda77597b128006674616f140f04df23264c6d1aa3", "sha1": "2ad14aed781c4a73ed4dbb421966d408a0a06686", "md5": "f43436d6bec321290f6af228ad602604"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle85", "version": "2.0.21"}]}, {"name": "gradle85SourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle85-sources.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle85-sources.jar", "size": 8517, "sha512": "f5e704414476452e2672558a6c11c7558c5153a5c8e3721cfe2549c4df05916dd9153a52443d4dd6dd63cae3c74908d03bcdf7706bcf86a58f4c7a06b8570fb2", "sha256": "59c819fbbab305889d75ec19bc2e61e39c163546944269c9dd18ce76412632ba", "sha1": "727acf04a3f280604db07a1c6d2374369eeb7b88", "md5": "77246242d939c2f28f622b77064492d7"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle85", "version": "2.0.21"}]}, {"name": "gradle85ApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle85"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle85.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle85.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle85", "version": "2.0.21"}]}, {"name": "gradle85RuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.plugin.api-version": "8.5", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-gradle-plugin-api", "version": {"requires": "2.0.21"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-reflect"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-script-runtime"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}], "requestedCapabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-api-gradle85"}]}], "files": [{"name": "kotlin-gradle-plugin-model-2.0.21-gradle85.jar", "url": "kotlin-gradle-plugin-model-2.0.21-gradle85.jar", "size": 13204, "sha512": "c4eab45a9c66c9088e64a0c9ca52e163576a9b5f38de2c67dd0a4cd4518310ccc37a8064882bee68801f2c4bd91c0836fec8ce68e2263337f817d64b40da933a", "sha256": "951d77989b357009631ff1efb12b0161ccf329c529c546a529f8a1d31f9bc03c", "sha1": "3672a6bafb29cdb53c10ca1e13f6c8c75af0f8eb", "md5": "58bd7d7c780ff77dee16295c16b65937"}], "capabilities": [{"group": "org.jetbrains.kotlin", "name": "kotlin-gradle-plugin-model-gradle85", "version": "2.0.21"}]}]}