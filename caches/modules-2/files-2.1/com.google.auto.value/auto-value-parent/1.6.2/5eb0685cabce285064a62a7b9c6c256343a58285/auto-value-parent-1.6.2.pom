<?xml version="1.0" encoding="UTF-8"?>
<!--
  Copyright (C) 2012 Google, Inc.

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.google.auto</groupId>
    <artifactId>auto-parent</artifactId>
    <version>6</version>
  </parent>

  <groupId>com.google.auto.value</groupId>
  <artifactId>auto-value-parent</artifactId>
  <version>1.6.2</version>
  <name>AutoValue Parent</name>
  <description>
    Immutable value-type code generation for Java 1.6+.
  </description>
  <packaging>pom</packaging>

  <scm>
    <url>http://github.com/google/auto</url>
    <connection>scm:git:git://github.com/google/auto.git</connection>
    <developerConnection>scm:git:ssh://**************/google/auto.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <modules>
    <module>annotations</module>
    <module>processor</module>
    <module>src/it/functional</module>
    <module>src/it/gwtserializer</module>
  </modules>
</project>
