{"formatVersion": "1.1", "component": {"url": "../../foundation-layout/1.6.8/foundation-layout-1.6.8.module", "group": "androidx.compose.foundation", "module": "foundation-layout", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}, "reason": "foundation-layout is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-layout-release.aar", "url": "foundation-layout-android-1.6.8.aar", "size": 526511, "sha512": "a852a0e1f2e8d1d0f663e7878256dbf4fba5667df2b0a81139d2478144e653d655dac2ca3af34c7800c36f5a697b9235729c5f757a83df4b30d6d1285624a6e7", "sha256": "5fc253188103bd0ce4f964e1747cbb6751236d1f3d63cab0df36c2a83a0a7298", "sha1": "1116984def629254a4778e2537b9a71e481ab108", "md5": "a1ee5c3d6d99526b7657c4aec3a021e4"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.2.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.core", "module": "core", "version": {"requires": "1.7.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}, "reason": "foundation-layout is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-layout-release.aar", "url": "foundation-layout-android-1.6.8.aar", "size": 526511, "sha512": "a852a0e1f2e8d1d0f663e7878256dbf4fba5667df2b0a81139d2478144e653d655dac2ca3af34c7800c36f5a697b9235729c5f757a83df4b30d6d1285624a6e7", "sha256": "5fc253188103bd0ce4f964e1747cbb6751236d1f3d63cab0df36c2a83a0a7298", "sha1": "1116984def629254a4778e2537b9a71e481ab108", "md5": "a1ee5c3d6d99526b7657c4aec3a021e4"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}, "reason": "foundation-layout is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-layout-android-1.6.8-sources.jar", "url": "foundation-layout-android-1.6.8-sources.jar", "size": 79330, "sha512": "4590692fa450bb0efddf0ed46ecb7b32f3503e8f14fd97901e09d9c10218fa47f8c91524b3afc00ac5c7e414de9b2b70a470fbca9bc0d05a5a3b46751a61aa28", "sha256": "1e6b08fba6647fa32f94fa8ff6337b565cc0158e395e1654ec05083eecc6f1eb", "sha1": "7abc26bf88267a59649360c2e68a13d69976ff17", "md5": "cfb53bdca64405f84c4c6d6902d43241"}]}]}