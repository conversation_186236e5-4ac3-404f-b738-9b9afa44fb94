{"formatVersion": "1.1", "component": {"url": "../../foundation/1.6.8/foundation-1.6.8.module", "group": "androidx.compose.foundation", "module": "foundation", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.collection", "module": "collection", "version": {"requires": "1.4.0"}}, {"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "reason": "foundation is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-release.aar", "url": "foundation-android-1.6.8.aar", "size": 4432298, "sha512": "252c467b3a3f76d4dfda45339626215ba1039d1ddd9f388b2224637d3acca9c69cdca95dce80e4aefc9ef1363e9a3b5e04352ce0436eee70af56bf9bb650dc0b", "sha256": "eded5b67247bd5db1f081602318032750ac26315bd2df88c6da619201dcb64b6", "sha1": "e89eb773236627b844b30aa577835656af12fcf9", "md5": "8ea73a14d511ee01e8ce31526befc29c"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.collection", "module": "collection", "version": {"requires": "1.4.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.core", "module": "core", "version": {"requires": "1.12.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.emoji2", "module": "emoji2", "version": {"requires": "1.3.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "reason": "foundation is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-release.aar", "url": "foundation-android-1.6.8.aar", "size": 4432298, "sha512": "252c467b3a3f76d4dfda45339626215ba1039d1ddd9f388b2224637d3acca9c69cdca95dce80e4aefc9ef1363e9a3b5e04352ce0436eee70af56bf9bb650dc0b", "sha256": "eded5b67247bd5db1f081602318032750ac26315bd2df88c6da619201dcb64b6", "sha1": "e89eb773236627b844b30aa577835656af12fcf9", "md5": "8ea73a14d511ee01e8ce31526befc29c"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "reason": "foundation is in atomic group androidx.compose.foundation"}], "files": [{"name": "foundation-android-1.6.8-sources.jar", "url": "foundation-android-1.6.8-sources.jar", "size": 740632, "sha512": "f40e2c81526d30e9aac2f2e68942390d11a661a0cefb632289831ec48fc74c55d1be5eb1630a0ece5b05c340e0465c4e5814bb9d80ef931c18456bdd46d04f48", "sha256": "47edc6699e8d6c3e35550db8ca420cd7d431e7624176343965e41a397027e94f", "sha1": "3689a919e5a57ced15b961efab7a333bcdc3a51c", "md5": "e5290ab43bd52a6fb474405ae46c8b35"}]}]}