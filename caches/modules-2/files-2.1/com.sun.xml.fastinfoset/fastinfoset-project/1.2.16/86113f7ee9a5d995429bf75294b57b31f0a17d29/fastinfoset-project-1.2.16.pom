<!--

    DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS HEADER.

    Copyright (c) 2012-2018 Oracle and/or its affiliates. All rights reserved.

    Oracle licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>org.eclipse.ee4j</groupId>
        <artifactId>project</artifactId>
        <version>1.0.5</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sun.xml.fastinfoset</groupId>
    <artifactId>fastinfoset-project</artifactId>
    <packaging>pom</packaging>
    <name>fastinfoset-project</name>
    <version>1.2.16</version>
    <description>Open Source implementation of the Fast Infoset Standard for Binary XML (http://www.itu.int/ITU-T/asn1/).</description>
    <url>https://projects.eclipse.org/projects/ee4j.jaxb-impl</url>

    <scm>
        <connection>scm:git:ssh://**************/eclipse-ee4j/jaxb-fi.git</connection>
        <developerConnection>scm:git:ssh://**************/eclipse-ee4j/jaxb-fi.git</developerConnection>
        <url>https://github.com/eclipse-ee4j/jaxb-fi</url>
        <tag>HEAD</tag>
    </scm>

    <licenses>
        <license>
            <name>Apache License, Version 2.0</name>
            <url>http://www.opensource.org/licenses/apache2.0.php</url>
        </license>
        <license>
            <name>Eclipse Distribution License - v 1.0</name>
            <url>http://www.eclipse.org/org/documents/edl-v10.php</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <id>bravehorsie</id>
            <name>Roman Grigoriadi</name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <issueManagement>
        <system>github</system>
        <url>https://github.com/eclipse-ee4j/jaxb-fi/issues</url>
    </issueManagement>

    <mailingLists>
        <mailingList>
            <name>Eclipse Implementation of JAXB mailing list</name>
            <post><EMAIL></post>
            <subscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-impl-dev</subscribe>
            <unsubscribe>https://dev.eclipse.org/mailman/listinfo/jaxb-impl-dev</unsubscribe>
            <archive>https://dev.eclipse.org/mhonarc/lists/jaxb-impl-dev</archive>
        </mailingList>
    </mailingLists>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <junit-version>4.12</junit-version>
        <maven-plugin.version>1.0.0</maven-plugin.version>
        <findbugs.exclude>${project.basedir}/exclude.xml</findbugs.exclude>
        <findbugs.skip>false</findbugs.skip>
        <findbugs.threshold>Low</findbugs.threshold>
        <legal.doc.source>${maven.multiModuleProjectDirectory}/..</legal.doc.source>
        <vendor.name>Oracle Corporation</vendor.name>
    </properties>

    <modules>
        <module>fastinfoset</module>
        <module>roundtrip-tests</module>
        <module>utilities</module>
        <module>samples</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.apache.maven</groupId>
                <artifactId>maven-plugin-tools-api</artifactId>
                <version>2.1</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish.jaxb</groupId>
                <artifactId>xsom</artifactId>
                <version>2.3.2</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.stream.buffer</groupId>
                <artifactId>streambuffer</artifactId>
                <version>1.5.7</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>

        <extensions>
            <extension>
                <groupId>org.apache.maven.wagon</groupId>
                <artifactId>wagon-webdav</artifactId>
                <version>1.0-beta-2</version>
            </extension>
        </extensions>

        <directory>target</directory>
        <finalName>${project.artifactId}-${project.version}</finalName>

        <pluginManagement>
            <plugins>
                <plugin>
                    <inherited>true</inherited>
                    <groupId>org.jvnet.maven-antrun-extended-plugin</groupId>
                    <artifactId>maven-antrun-extended-plugin</artifactId>
                    <version>1.43</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>cobertura-maven-plugin</artifactId>
                    <version>2.7</version>
                    <configuration>
                        <aggregate>true</aggregate>
                        <formats>
                            <format>xml</format>
                        </formats>
                        <check>
                            <totalLineRate>35</totalLineRate>
                            <!--packageLineRate>45</packageLineRate-->
                            <haltOnFailure>true</haltOnFailure>
                        </check>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.glassfish.copyright</groupId>
                    <artifactId>glassfish-copyright-maven-plugin</artifactId>
                    <version>1.50</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>3.0.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>findbugs-maven-plugin</artifactId>
                    <version>3.0.5</version>
                    <configuration>
                        <skip>${findbugs.skip}</skip>
                        <threshold>${findbugs.threshold}</threshold>
                        <findbugsXmlWithMessages>true</findbugsXmlWithMessages>
                        <excludeFilterFile>
                            exclude-common.xml,${findbugs.exclude}
                        </excludeFilterFile>
                        <fork>true</fork>
                        <jvmArgs>-Xms64m -Xmx256m</jvmArgs>    
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.glassfish.findbugs</groupId>
                            <artifactId>findbugs</artifactId>
                            <version>1.0</version>
                        </dependency>
                    </dependencies>
                </plugin>
                <plugin>
                    <inherited>true</inherited>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>1.6</source>
                        <target>1.6</target>
                        <compilerArgument>${maven.compiler.argument}</compilerArgument>
                    </configuration>
                    <version>3.8.0</version>
                </plugin>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>buildnumber-maven-plugin</artifactId>
                    <version>1.4</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.felix</groupId>
                    <artifactId>maven-bundle-plugin</artifactId>
                    <version>3.5.1</version>
                    <configuration>
                        <instructions>
                            <Implementation-Version>${project.version}</Implementation-Version>
                            <Implementation-Build-Id>${project.version}-${buildNumber}, ${timestamp}</Implementation-Build-Id>
                        </instructions>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-enforcer-plugin</artifactId>
                    <version>3.0.0-M2</version>
                </plugin>               
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-gpg-plugin</artifactId>
                    <version>1.6</version>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <configuration>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <timestampFormat>{0,date,yyyy-MM-dd'T'HH:mm:ssZ}</timestampFormat>
                    <getRevisionOnlyOnce>true</getRevisionOnlyOnce>
                    <revisionOnScmFailure>false</revisionOnScmFailure>
                    <shortRevisionLength>7</shortRevisionLength>
                </configuration>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.glassfish.copyright</groupId>
                <artifactId>glassfish-copyright-maven-plugin</artifactId>
                <configuration>
                    <scm>git</scm>
                    <templateFile>copyright.txt</templateFile>
                    <excludeFile>copyright-exclude</excludeFile>
                    <!-- skip files not under SCM-->
                    <scmOnly>true</scmOnly>
                    <!-- turn off warnings -->
                    <warn>true</warn>
                    <!-- for use with repair -->
                    <update>false</update>
                    <!-- check that year is correct -->
                    <ignoreYear>false</ignoreYear>
                </configuration>
                <executions>
                    <execution>
                        <id>copyright-check</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-legal-resource</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>add-resource</goal>
                        </goals>
                        <configuration>
                            <resources>
                                <resource>
                                    <directory>${legal.doc.source}</directory>
                                    <includes>
                                        <include>NOTICE.md</include>
                                        <include>LICENSE</include>
                                    </includes>
                                    <targetPath>META-INF</targetPath>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>

    </build>

    <reporting>
        <outputDirectory>target/site</outputDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <aggregate>true</aggregate>
                    <debug>true</debug>
                    <links>
                        <link>http://java.sun.com/j2se/1.5.0/docs/api</link>
                    </links>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <configuration>
                    <aggregate>true</aggregate>
                </configuration>
                <version>2.5</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-project-info-reports-plugin</artifactId>
                <version>3.0.0</version>
                <reportSets>
                    <reportSet>
                        <reports>
                            <report>project-team</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
                <configuration>
                    <xmlOutput>true</xmlOutput>
                    <xmlOutputDirectory>${project.reporting.outputDirectory}</xmlOutputDirectory>
                </configuration>
            </plugin>
        </plugins>
    </reporting>

</project>
