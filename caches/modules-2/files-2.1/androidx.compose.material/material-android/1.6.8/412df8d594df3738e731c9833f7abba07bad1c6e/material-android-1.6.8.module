{"formatVersion": "1.1", "component": {"url": "../../material/1.6.8/material-1.6.8.module", "group": "androidx.compose.material", "module": "material", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-release.aar", "url": "material-android-1.6.8.aar", "size": 2991369, "sha512": "72281b6da1963fe5ad00f7e1ad523517f0144e79475377b15b01648f1bf80155c8ed8a8b8091b17f7832b296acb4b363ee22d3d013ad1aef23cf56d5a0f6fa4a", "sha256": "4f4de347043c2c202627d743c16487170627657fea2a7df7ec94cf4786e43015", "sha1": "bd98438759a8fa9004372660d5246661fcd94949", "md5": "f9eb20c1499d44aa063b2de2f5b74997"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.lifecycle", "module": "lifecycle-runtime", "version": {"requires": "2.6.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.lifecycle", "module": "lifecycle-viewmodel", "version": {"requires": "2.6.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.savedstate", "module": "savedstate", "version": {"requires": "1.2.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-release.aar", "url": "material-android-1.6.8.aar", "size": 2991369, "sha512": "72281b6da1963fe5ad00f7e1ad523517f0144e79475377b15b01648f1bf80155c8ed8a8b8091b17f7832b296acb4b363ee22d3d013ad1aef23cf56d5a0f6fa4a", "sha256": "4f4de347043c2c202627d743c16487170627657fea2a7df7ec94cf4786e43015", "sha1": "bd98438759a8fa9004372660d5246661fcd94949", "md5": "f9eb20c1499d44aa063b2de2f5b74997"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-android-1.6.8-sources.jar", "url": "material-android-1.6.8-sources.jar", "size": 239762, "sha512": "9b7d9c6aeb19a399e8bddd0fa1ba35872b456fa651cf689b88220c2c7e65f0bba1322b2c9ca7180e90142745b25fd8e2535bd5986d8e37cd9805c3c9b5c581dd", "sha256": "80027f931fb14ff82936954b967b1aedeed525f3b400af360901f041ddde6964", "sha1": "864313b7a0766a4615bf1eca76e715409e7952e0", "md5": "ed465e0bb59b3399747df7feaae33eb7"}]}]}