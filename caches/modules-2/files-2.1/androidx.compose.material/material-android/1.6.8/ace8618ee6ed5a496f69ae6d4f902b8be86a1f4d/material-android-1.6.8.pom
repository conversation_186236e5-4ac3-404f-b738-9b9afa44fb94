<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.compose.material</groupId>
  <artifactId>material-android</artifactId>
  <version>1.6.8</version>
  <packaging>aar</packaging>
  <name>Compose Material Components</name>
  <description>Compose Material Design Components library</description>
  <url>https://developer.android.com/jetpack/androidx/releases/compose-material#1.6.8</url>
  <inceptionYear>2018</inceptionYear>
  <organization>
    <name>The Android Open Source Project</name>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://android.googlesource.com/platform/frameworks/support</connection>
    <url>https://cs.android.com/androidx/platform/frameworks/support</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.compose.material</groupId>
        <artifactId>material-icons-core</artifactId>
        <version>1.6.8</version>
      </dependency>
      <dependency>
        <groupId>androidx.compose.material</groupId>
        <artifactId>material-icons-extended</artifactId>
        <version>1.6.8</version>
      </dependency>
      <dependency>
        <groupId>androidx.compose.material</groupId>
        <artifactId>material-ripple</artifactId>
        <version>1.6.8</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>androidx.annotation</groupId>
      <artifactId>annotation</artifactId>
      <version>1.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.animation</groupId>
      <artifactId>animation-android</artifactId>
      <version>1.6.8</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>androidx.compose.animation</groupId>
      <artifactId>animation-core-android</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.foundation</groupId>
      <artifactId>foundation-android</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.foundation</groupId>
      <artifactId>foundation-layout-android</artifactId>
      <version>1.6.8</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material</groupId>
      <artifactId>material-icons-core</artifactId>
      <version>[1.6.8]</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material</groupId>
      <artifactId>material-ripple</artifactId>
      <version>[1.6.8]</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.runtime</groupId>
      <artifactId>runtime-android</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-android</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-text-android</artifactId>
      <version>1.6.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-util-android</artifactId>
      <version>1.6.8</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-runtime</artifactId>
      <version>2.6.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.lifecycle</groupId>
      <artifactId>lifecycle-viewmodel</artifactId>
      <version>2.6.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
      <type>aar</type>
    </dependency>
    <dependency>
      <groupId>androidx.savedstate</groupId>
      <artifactId>savedstate</artifactId>
      <version>1.2.1</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-common</artifactId>
      <version>1.8.22</version>
      <scope>runtime</scope>
      <exclusions>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-stdlib-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-common</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-test-annotations-common</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>