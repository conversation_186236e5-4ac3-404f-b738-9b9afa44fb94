{"formatVersion": "1.1", "component": {"group": "androidx.compose.material", "module": "material-icons-core", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}], "files": [{"name": "material-icons-core-1.6.8-multiplatform-sources.jar", "url": "material-icons-core-1.6.8-multiplatform-sources.jar", "size": 557149, "sha512": "ee5515512480b2adfb7c17f6114e4ab06e952d1d478b52cebd436794b65fc84b0216a183716009e42faf436b7f0e09bdea271aae1a949d3041a848eacb236652", "sha256": "4d5c5060b50f621a87c0fd7253044400afa4766fad6e77a36d109c308694a0cb", "sha1": "15f5de1f642bb10c7bed0878db53488587b05cc2", "md5": "a193b50db440bdd323b1ab5faab9dea7"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}], "files": [{"name": "apiLevels.json", "url": "material-icons-core-1.6.8-versionMetadata.json", "size": 58794, "sha512": "615a544e09cb22fef12cdff3c97a38e93969f191e9588817ea3da94c4e5bb9aad74429be091d61e834a945f38b04ecbcaae16d1406b1cc4d461428468077fe27", "sha256": "ef23dbabeaa05c531b121751c5c6cd1247859fa042a00eda58914b3c5fb1db67", "sha1": "6bd65f97f82293b642f63f7f77c637ace3129113", "md5": "dbfa54c8a0340b54b260d20ad82fd70a"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}], "files": [{"name": "material-icons-core-metadata-1.6.8.jar", "url": "material-icons-core-1.6.8.jar", "size": 721, "sha512": "26643bb16c8eae588385fa05899112b348a849afbfb709341ca5bf03e131e167f93dd7c1fce3a4836a945eb93d2d976ba38986ce7ad0fbf58b0e63c62a130a3d", "sha256": "951f2a3a6c0913819dfaae7c69cb8cdf977f7c79bd53fef03e4faf459ee30a0f", "sha1": "32a0e6996da0950faefd49b848a3ea3d1df99342", "md5": "f693b4cdfac9d3ed6db0470dbc0abaeb"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material-icons-core is in atomic group androidx.compose.material"}], "files": [{"name": "material-icons-core-kotlin-1.6.8-sources.jar", "url": "material-icons-core-1.6.8-sources.jar", "size": 284048, "sha512": "2803312f6e4dfb1b2501b44f47eb72fd02212e41a18c5dcbec7b7e280612b486d23b72d50688a2fb3776b1582ae63ec923fd3f39bdbc4e951a779dc133495f64", "sha256": "b3f1be7fda4d13a6d938bb918275ddc143e9d6a2007727f284778480526f5f16", "sha1": "e518df0d8a19c3833f72a24ff667853015e340fd", "md5": "6f8dfe229b864de0e1deaa71ad0c1434"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-icons-core-android/1.6.8/material-icons-core-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-icons-core-android/1.6.8/material-icons-core-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-icons-core-android/1.6.8/material-icons-core-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-icons-core-desktop/1.6.8/material-icons-core-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-icons-core-desktop/1.6.8/material-icons-core-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-icons-core-desktop/1.6.8/material-icons-core-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-icons-core-desktop", "version": "1.6.8"}}]}