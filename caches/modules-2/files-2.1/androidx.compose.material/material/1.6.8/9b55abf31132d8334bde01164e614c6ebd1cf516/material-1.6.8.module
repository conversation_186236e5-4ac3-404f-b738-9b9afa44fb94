{"formatVersion": "1.1", "component": {"group": "androidx.compose.material", "module": "material", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-1.6.8-multiplatform-sources.jar", "url": "material-1.6.8-multiplatform-sources.jar", "size": 250044, "sha512": "6f44909550bb6ef522c0b36ea72377e7bcd2663ac4b6c60c2aea63f5e4f82c96485e89d766efea381e4d9ce6f3e85e0ab09c2f9c7283e6e9a519a3e052d6a479", "sha256": "3870e3d3de9082ea4ebd28e188b5dfac00b9d38c58bbb2d90d2f5d4525716c55", "sha1": "9e959a31db29bf8e9f59aad977dff8722eb85c15", "md5": "1b81b5f2d6ade9431628b50f3523bb2b"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "apiLevels.json", "url": "material-1.6.8-versionMetadata.json", "size": 84791, "sha512": "80f672908adb2cdb29bac424e992de0ac20594b896f984f5852e37267a54bc87df3c458751a420fd999e52679d54edd303eb495623e5be021b652e11e72524c0", "sha256": "fa604cf7b3792296d6a15da5c863842ed2d425f5c550632a81f65e071a9ddb84", "sha1": "a70112cceaa0afe9c414ae11232086af0038ccd3", "md5": "fcd8686bcd780c3e2cf613ce443c3a75"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-metadata-1.6.8.jar", "url": "material-1.6.8.jar", "size": 783, "sha512": "4d421d5e437bfd71e9c05ac9536fbf728f7be6652fd7be66bba72e4429ef4b139a35e4b8b8667deb3968e030f404c369644a610ecb7a929247db9b83ca3bbdda", "sha256": "eca64a1631de1d4a5eaeb98783cb983159cc31b3dcb0820df0ba399156292abc", "sha1": "44fd7b9a7d1568b844faa3ffef9879f12bff7d48", "md5": "e0bd1d4d20775c25b391f6dc3cacd557"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.8"}, "reason": "material is in atomic group androidx.compose.material"}], "files": [{"name": "material-kotlin-1.6.8-sources.jar", "url": "material-1.6.8-sources.jar", "size": 219922, "sha512": "bc670ecbeb3523ea82fd4596643964c8aeb9ccee9aa8f63abf6c1dd62b0ad29d605ae4547132caae223508207c454b6d697812f2817e5418332ed27f763b07c9", "sha256": "5aef130100dffcc331577acc963c09e1d0cd664b173073a3fd5d69b5f24dbfe0", "sha1": "32e72587287ddc817aa175a99ffa078026bc4291", "md5": "0e36bd4d318bbcd46d65a781bd9ceefa"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-android/1.6.8/material-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-android/1.6.8/material-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../material-android/1.6.8/material-android-1.6.8.module", "group": "androidx.compose.material", "module": "material-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-desktop/1.6.8/material-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-desktop/1.6.8/material-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../material-desktop/1.6.8/material-desktop-1.6.8.module", "group": "androidx.compose.material", "module": "material-desktop", "version": "1.6.8"}}]}