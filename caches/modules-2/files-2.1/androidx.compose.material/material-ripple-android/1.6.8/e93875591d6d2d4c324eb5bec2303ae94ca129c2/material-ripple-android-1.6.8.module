{"formatVersion": "1.1", "component": {"url": "../../material-ripple/1.6.8/material-ripple-1.6.8.module", "group": "androidx.compose.material", "module": "material-ripple", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}], "files": [{"name": "material-ripple-release.aar", "url": "material-ripple-android-1.6.8.aar", "size": 73206, "sha512": "9d79849a8f4634da70f49de1ee2bd4219830980d1a7263f1f669db93a57e0c5227301fec4c8c0f6c6b47b03eb74aec94689fcac41d400db8a5fede5d96944fb1", "sha256": "7a7f9572a020fd83c454a2079cfd16f3f7cedf4c10f04d15cc06e371877bfe23", "sha1": "f2fb6a056a6de731cbe709d6ce34e717a4b3b692", "md5": "b3a87db2ba0f1c75a042857a950c129f"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}], "files": [{"name": "material-ripple-release.aar", "url": "material-ripple-android-1.6.8.aar", "size": 73206, "sha512": "9d79849a8f4634da70f49de1ee2bd4219830980d1a7263f1f669db93a57e0c5227301fec4c8c0f6c6b47b03eb74aec94689fcac41d400db8a5fede5d96944fb1", "sha256": "7a7f9572a020fd83c454a2079cfd16f3f7cedf4c10f04d15cc06e371877bfe23", "sha1": "f2fb6a056a6de731cbe709d6ce34e717a4b3b692", "md5": "b3a87db2ba0f1c75a042857a950c129f"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.material", "module": "material", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}, {"group": "androidx.compose.material", "module": "material-icons-extended", "version": {"requires": "1.6.8"}, "reason": "material-ripple is in atomic group androidx.compose.material"}], "files": [{"name": "material-ripple-android-1.6.8-sources.jar", "url": "material-ripple-android-1.6.8-sources.jar", "size": 23223, "sha512": "f95da268f90a671eaa6ff66c79b66f431197fc7e7582386e64a914658b490ac08abce189cbb5322db4aed203b6857a34fef1967ff43035330937ae00edd14a5c", "sha256": "6bc29ab177ee9c9db20b06ea1fdbbed612f9a9fae0ec6885fb6d86781fbad7f3", "sha1": "5658994fca2cfab39445d8960d9609737b5f71be", "md5": "44bd50910312abb6a6d10efa9dcb7976"}]}]}