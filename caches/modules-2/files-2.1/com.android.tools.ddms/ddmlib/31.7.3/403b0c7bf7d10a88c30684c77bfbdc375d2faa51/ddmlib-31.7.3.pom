<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.android.tools.ddms</groupId>
  <artifactId>ddmlib</artifactId>
  <version>31.7.3</version>
  <name>Android Tools ddmlib</name>
  <description>Library providing APIs to talk to Android devices</description>
  <url>http://tools.android.com/</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>common</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.22.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>net.sf.kxml</groupId>
      <artifactId>kxml2</artifactId>
      <version>2.3.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains</groupId>
      <artifactId>annotations</artifactId>
      <version>23.0.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
