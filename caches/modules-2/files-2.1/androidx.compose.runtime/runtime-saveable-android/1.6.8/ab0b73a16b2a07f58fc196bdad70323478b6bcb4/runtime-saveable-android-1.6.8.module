{"formatVersion": "1.1", "component": {"url": "../../runtime-saveable/1.6.8/runtime-saveable-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-saveable", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-saveable-release.aar", "url": "runtime-saveable-android-1.6.8.aar", "size": 1185053, "sha512": "cef65fbb1e0cdf51961fc70b1cd00fd318b690fa27c7cdf15cb11b4c6d677239c81974257beea2550f7135f5d994334cdb64c5c020f2440dd81298393289db06", "sha256": "ee7ac18bd1385f04320dbddf12250fc341a1fa5ce47401c81442c9c3dca97daf", "sha1": "0179a9d4c93f63b76814a0c29030dc42d8812b19", "md5": "18ba0b062bb688f46b2a54470178100a"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-saveable-release.aar", "url": "runtime-saveable-android-1.6.8.aar", "size": 1185053, "sha512": "cef65fbb1e0cdf51961fc70b1cd00fd318b690fa27c7cdf15cb11b4c6d677239c81974257beea2550f7135f5d994334cdb64c5c020f2440dd81298393289db06", "sha256": "ee7ac18bd1385f04320dbddf12250fc341a1fa5ce47401c81442c9c3dca97daf", "sha1": "0179a9d4c93f63b76814a0c29030dc42d8812b19", "md5": "18ba0b062bb688f46b2a54470178100a"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime-saveable is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-saveable-android-1.6.8-sources.jar", "url": "runtime-saveable-android-1.6.8-sources.jar", "size": 11533, "sha512": "8cc3a64fa5c6646a9fb613af48895817a462f4143e4a8aaf5c7ce65ebc5566ee87fdae168c397284334f6615738e79ca27d40b4e67f1a73162a3bc48ddef9969", "sha256": "909f6593a4be287cb6dbbf5dd0ca87a4af9d37b3d9330c91f7c80d0bcc7ec446", "sha1": "e36ff9886898fcb4c455c2e9901391f49c6574b5", "md5": "7959de782d8601a457ad87ca391a3dbe"}]}]}