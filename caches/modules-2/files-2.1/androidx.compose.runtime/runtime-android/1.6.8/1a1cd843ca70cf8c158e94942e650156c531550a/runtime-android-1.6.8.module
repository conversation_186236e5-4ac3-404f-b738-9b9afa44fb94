{"formatVersion": "1.1", "component": {"url": "../../runtime/1.6.8/runtime-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.7.1"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}}], "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-release.aar", "url": "runtime-android-1.6.8.aar", "size": 2429485, "sha512": "b0f11d246acea57ab8a7ee11f3823154b8e3f8fc95d0c0fc7b95b4d5554c64117d92f14db20cc7d01f4bac9b2ef96671c24821025e8ce78c5f44f27e8c924de8", "sha256": "a431ba9358d956a19cb020f2ce3c2b14879c421408aaec5fda9644cdf7b50aa7", "sha1": "affd49c7395c4aa06adf14918737b3fcdd5f7fc6", "md5": "d955afdda446f943d5f63094aa6b7b08"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.collection", "module": "collection", "version": {"requires": "1.4.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.7.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-release.aar", "url": "runtime-android-1.6.8.aar", "size": 2429485, "sha512": "b0f11d246acea57ab8a7ee11f3823154b8e3f8fc95d0c0fc7b95b4d5554c64117d92f14db20cc7d01f4bac9b2ef96671c24821025e8ce78c5f44f27e8c924de8", "sha256": "a431ba9358d956a19cb020f2ce3c2b14879c421408aaec5fda9644cdf7b50aa7", "sha1": "affd49c7395c4aa06adf14918737b3fcdd5f7fc6", "md5": "d955afdda446f943d5f63094aa6b7b08"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-android-1.6.8-sources.jar", "url": "runtime-android-1.6.8-sources.jar", "size": 371376, "sha512": "2b7bbc5ad5c4a228d2fdc407fd7e162c99eb93725079f17c3256f08a0d68226b917638e06352056284a5bd91134ff4225e4ec66e5e92a8f74f312d40f791b4c4", "sha256": "f824e4758db8d1d736bda0d7a21034b220a6c03eb729cc8c8731eee9d7549902", "sha1": "7cf63e5b791fa66bd7142a71fd20fde58162a47e", "md5": "5228ff7ddc27e22e43dc5a45fbdfdb3d"}]}]}