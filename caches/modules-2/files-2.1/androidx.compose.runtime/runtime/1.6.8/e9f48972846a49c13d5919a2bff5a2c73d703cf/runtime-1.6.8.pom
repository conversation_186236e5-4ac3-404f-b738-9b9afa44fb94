<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>radle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.compose.runtime</groupId>
  <artifactId>runtime</artifactId>
  <version>1.6.8</version>
  <packaging>aar</packaging>
  <name>Compose Runtime</name>
  <description>Tree composition support for code generated by the Compose compiler plugin and corresponding public API</description>
  <url>https://developer.android.com/jetpack/androidx/releases/compose-runtime#1.6.8</url>
  <inceptionYear>2019</inceptionYear>
  <organization>
    <name>The Android Open Source Project</name>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://android.googlesource.com/platform/frameworks/support</connection>
    <url>https://cs.android.com/androidx/platform/frameworks/support</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.compose.runtime</groupId>
        <artifactId>runtime-livedata</artifactId>
        <version>1.6.8</version>
      </dependency>
      <dependency>
        <groupId>androidx.compose.runtime</groupId>
        <artifactId>runtime-rxjava2</artifactId>
        <version>1.6.8</version>
      </dependency>
      <dependency>
        <groupId>androidx.compose.runtime</groupId>
        <artifactId>runtime-rxjava3</artifactId>
        <version>1.6.8</version>
      </dependency>
      <dependency>
        <groupId>androidx.compose.runtime</groupId>
        <artifactId>runtime-saveable</artifactId>
        <version>1.6.8</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>androidx.compose.runtime</groupId>
      <artifactId>runtime-android</artifactId>
      <version>1.6.8</version>
      <type>aar</type>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.7.1</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>