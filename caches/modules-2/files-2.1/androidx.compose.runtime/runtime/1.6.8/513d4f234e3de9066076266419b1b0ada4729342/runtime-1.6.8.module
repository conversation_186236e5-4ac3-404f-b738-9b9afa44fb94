{"formatVersion": "1.1", "component": {"group": "androidx.compose.runtime", "module": "runtime", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-1.6.8-multiplatform-sources.jar", "url": "runtime-1.6.8-multiplatform-sources.jar", "size": 376469, "sha512": "7e08663082df2fa72aa45b14b8ffbfc110e31d480e6d8468c17c8bbd48b589cc4235316c800b4653309c7fd4110dfb559720709eae9d499464f94f8c52aa0965", "sha256": "006117d0696f65af2d440f704f706822d5aa21447a2024e1cf393df27b738469", "sha1": "44cbc325c378ba119d48386ea585ca0ec60e68c1", "md5": "c776fd373604d92e468c50d365ac64db"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "apiLevels.json", "url": "runtime-1.6.8-versionMetadata.json", "size": 61495, "sha512": "dcdfbb60ef541a0fd723a9ee0e0bcedc70efa2579c39ffeec45c861a38b04616c687d333d09842e7b140920460c2c22c54921e5e61b01718e9e6184e79351eec", "sha256": "d57058c8f6fec9bf0c3d644513fddcfce6d03e2bdd154c4b2521a2efae150b58", "sha1": "fc9ba44377454991245ab9394371fb18f1524070", "md5": "1a0d9edb4fb765eb1178c1fb96f0317e"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}}], "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-metadata-1.6.8.jar", "url": "runtime-1.6.8.jar", "size": 764, "sha512": "dab4a94fc7eda019bca1d06a4057a61aecdc487f50bb19b8ac49ead521d2f91f74a0b8513465f5e7d333062e4419a93695a8fc8c3d2e3e9e4112fe6360365521", "sha256": "9f40ab0b8cf4e7cb0f593493c2ed96e7155d0e91eb592407597ecd61e2d5a054", "sha1": "39912aaa32797bd642aa4ef543a834c884143412", "md5": "377754d56814d9739a8332b2b1e65b8a"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.runtime", "module": "runtime-livedata", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava2", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-rxjava3", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}, {"group": "androidx.compose.runtime", "module": "runtime-saveable", "version": {"requires": "1.6.8"}, "reason": "runtime is in atomic group androidx.compose.runtime"}], "files": [{"name": "runtime-kotlin-1.6.8-sources.jar", "url": "runtime-1.6.8-sources.jar", "size": 361442, "sha512": "5f688863dfca9a6782b245a9e933768cad2c67ea91ad289ebd6d5433d8ed6425eb80c1de51863bfe07acfc008701dc62fe37678a390fb5fcf8db02cc87cc90b4", "sha256": "34f1694f44b214ec5733b3deb1a49a80a14aa7534677a08be5d57795fdb8063e", "sha1": "7344761acd62e5b15f37bd8d2fd87082eeb347f2", "md5": "0b898baaaff480d5a0e4a98ffb37f7a4"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../runtime-android/1.6.8/runtime-android-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../runtime-android/1.6.8/runtime-android-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../runtime-android/1.6.8/runtime-android-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../runtime-desktop/1.6.8/runtime-desktop-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../runtime-desktop/1.6.8/runtime-desktop-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../runtime-desktop/1.6.8/runtime-desktop-1.6.8.module", "group": "androidx.compose.runtime", "module": "runtime-desktop", "version": "1.6.8"}}]}