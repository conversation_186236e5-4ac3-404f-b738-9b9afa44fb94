{"formatVersion": "1.1", "component": {"url": "../../material3/1.2.1/material3-1.2.1.module", "group": "androidx.compose.material3", "module": "material3", "version": "1.2.1", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.6-rc-1"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.annotation", "module": "annotation-experimental", "version": {"requires": "1.4.0"}}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.ui", "module": "ui-graphics", "version": {"requires": "1.6.0"}}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.0"}}], "dependencyConstraints": [{"group": "androidx.compose.material3", "module": "material3-window-size-class", "version": {"requires": "1.2.1"}, "reason": "material3 is in atomic group androidx.compose.material3"}], "files": [{"name": "material3-release.aar", "url": "material3-android-1.2.1.aar", "size": 4738857, "sha512": "975442ece66c77cc0f307caddb856c6fc5a91c3ebdb594dc9b3ce96fe3aff904a0c05e25e1caf76f4ace24e53d9b61f4e7c52c1adb43c8862c90344cd2d8695b", "sha256": "d344e0d73060cde4bbc0ed64146986ca1fae0e0ec953ad9698713edd2bd11600", "sha1": "bbb13fec1193eecdeccb40452ff184826f4385a2", "md5": "fda3199e76b8ffb3d440994e5f231585"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.activity", "module": "activity-compose", "version": {"requires": "1.5.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.annotation", "module": "annotation-experimental", "version": {"requires": "1.4.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.collection", "module": "collection", "version": {"requires": "1.4.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.material", "module": "material-icons-core", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.material", "module": "material-ripple", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-graphics", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-text", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.lifecycle", "module": "lifecycle-common-java8", "version": {"requires": "2.6.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.lifecycle", "module": "lifecycle-runtime", "version": {"requires": "2.6.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.lifecycle", "module": "lifecycle-viewmodel", "version": {"requires": "2.6.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.savedstate", "module": "savedstate-ktx", "version": {"requires": "1.2.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.material3", "module": "material3-window-size-class", "version": {"requires": "1.2.1"}, "reason": "material3 is in atomic group androidx.compose.material3"}], "files": [{"name": "material3-release.aar", "url": "material3-android-1.2.1.aar", "size": 4738857, "sha512": "975442ece66c77cc0f307caddb856c6fc5a91c3ebdb594dc9b3ce96fe3aff904a0c05e25e1caf76f4ace24e53d9b61f4e7c52c1adb43c8862c90344cd2d8695b", "sha256": "d344e0d73060cde4bbc0ed64146986ca1fae0e0ec953ad9698713edd2bd11600", "sha1": "bbb13fec1193eecdeccb40452ff184826f4385a2", "md5": "fda3199e76b8ffb3d440994e5f231585"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.material3", "module": "material3-window-size-class", "version": {"requires": "1.2.1"}, "reason": "material3 is in atomic group androidx.compose.material3"}], "files": [{"name": "material3-android-1.2.1-sources.jar", "url": "material3-android-1.2.1-sources.jar", "size": 461800, "sha512": "c4482d7ab142eeafbf3b0de4f80fb36657ba7918cb2ae193987745469089777969799013752a83151b3fa862c03a574ee7798136994c394ee09ef74d09d1f40a", "sha256": "6871f6f902595b6241e4b62cd3ccdf98bc09c6b16f16539b25ae2732e4383538", "sha1": "a269bcec123bcf8567c612a65f19cc47d9ab2b49", "md5": "8a41ac70b8ebebfde05c78321d47a9a4"}]}]}