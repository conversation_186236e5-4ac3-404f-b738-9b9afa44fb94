<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>androidx.compose.material3</groupId>
  <artifactId>material3</artifactId>
  <version>1.2.1</version>
  <packaging>aar</packaging>
  <name>Compose Material3 Components</name>
  <description>Compose Material You Design Components library</description>
  <url>https://developer.android.com/jetpack/androidx/releases/compose-material3#1.2.1</url>
  <inceptionYear>2021</inceptionYear>
  <organization>
    <name>The Android Open Source Project</name>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://android.googlesource.com/platform/frameworks/support</connection>
    <url>https://cs.android.com/androidx/platform/frameworks/support</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>androidx.compose.material3</groupId>
        <artifactId>material3-window-size-class</artifactId>
        <version>1.2.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>androidx.compose.foundation</groupId>
      <artifactId>foundation</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.foundation</groupId>
      <artifactId>foundation-layout</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material</groupId>
      <artifactId>material-icons-core</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material</groupId>
      <artifactId>material-ripple</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.material3</groupId>
      <artifactId>material3-android</artifactId>
      <version>1.2.1</version>
      <type>aar</type>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.runtime</groupId>
      <artifactId>runtime</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-graphics</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui-text</artifactId>
      <version>1.6.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>