{"formatVersion": "1.1", "component": {"url": "../../kotlinx-coroutines-test/1.7.3/kotlinx-coroutines-test-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-test", "version": "1.7.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.4.2"}}, "variants": [{"name": "jvmApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.jetbrains", "module": "annotations", "version": {"requires": "23.0.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-bom", "version": {"requires": "1.7.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.8.20"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.3"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.20"}}], "files": [{"name": "kotlinx-coroutines-test-jvm-1.7.3.jar", "url": "kotlinx-coroutines-test-jvm-1.7.3.jar", "size": 159243, "sha512": "7555931a376e3940b3301ca021c5f83e0a90587cbfc75987fcfbfe8309785626f3b24b92ccf725d40b69a29e4a31fe18946fc8ecc2088e38d0df178ef2b4e044", "sha256": "d19742a65dbdabc9ffdcb9f84a15c776e511a14da602d54ea4e078d6f7227a26", "sha1": "89e34400f452dab68fbb3caa66d854c89aaafa07", "md5": "b874ff6ea64c2f728a3721bcdd400267"}]}, {"name": "jvmRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.jetbrains", "module": "annotations", "version": {"requires": "23.0.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-bom", "version": {"requires": "1.7.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.8.20"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.3"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.20"}}], "files": [{"name": "kotlinx-coroutines-test-jvm-1.7.3.jar", "url": "kotlinx-coroutines-test-jvm-1.7.3.jar", "size": 159243, "sha512": "7555931a376e3940b3301ca021c5f83e0a90587cbfc75987fcfbfe8309785626f3b24b92ccf725d40b69a29e4a31fe18946fc8ecc2088e38d0df178ef2b4e044", "sha256": "d19742a65dbdabc9ffdcb9f84a15c776e511a14da602d54ea4e078d6f7227a26", "sha1": "89e34400f452dab68fbb3caa66d854c89aaafa07", "md5": "b874ff6ea64c2f728a3721bcdd400267"}]}, {"name": "jvmSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "files": [{"name": "kotlinx-coroutines-test-jvm-1.7.3-sources.jar", "url": "kotlinx-coroutines-test-jvm-1.7.3-sources.jar", "size": 38354, "sha512": "65a87d7790b22a1274686cf4efc37f3cf431deccd95ee5f09d58414a1a2337957cd853a2c5f7bdfc4afc323647b3fd1cf01948bd88e21d472086b9cd8169e7c2", "sha256": "58bfd593f0644c9f1f047e3ee851ab179a663edad29754bb984f581ce290419d", "sha1": "487eb76a3b0eafdedcc6df4e5c3e201f2e967b57", "md5": "eae268b628fa37aa51d080093a28ad4c"}]}]}