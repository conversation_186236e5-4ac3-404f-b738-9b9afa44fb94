{"formatVersion": "1.1", "component": {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": "1.7.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.4.2"}}, "variants": [{"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.20"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.8.20"}}, {"group": "org.jetbrains.kotlinx", "module": "atomicfu", "version": {"requires": "0.21.0"}}], "files": [{"name": "kotlinx-coroutines-core-metadata-1.7.3.jar", "url": "kotlinx-coroutines-core-1.7.3.jar", "size": 87702, "sha512": "98a2570c4fb5cf2b0c78be731367b219e66c6e23f7fac4218ec447104266ee46714c3c233686fb8a2525c0a9829250dce8b72df7f53ff6881460b241d8f6a462", "sha256": "f9522095aedcc2a6ab32c7484061ea698352c71be1390adb403b59aa48a38fdc", "sha1": "e4e63122407750bcfae0f646bef4cd5df85e0a72", "md5": "f838b601e6c21a679580f38649ddedca"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "files": [{"name": "kotlinx-coroutines-core-kotlin-1.7.3-sources.jar", "url": "kotlinx-coroutines-core-1.7.3-sources.jar", "size": 316881, "sha512": "43d02ab2fe2746612857af2de225c8b5854f8f24fb9c06d3ef546717cda64dfd644f6baac6d6514ca91b53ee230b100cad9faad7ccc5cd741e6a1eaf7c1512bc", "sha256": "86ce259182afe7dd82cfe97da50a736a6194a91cfe19b8336799890bbd0e81b1", "sha1": "e968932da200f1626bdf480dd55cca94755a8078", "md5": "9c50e9227d877ff594f1dab2f7bc1dc6"}]}, {"name": "androidNativeArm32ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "android_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativearm32/1.7.3/kotlinx-coroutines-core-androidnativearm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativearm32", "version": "1.7.3"}}, {"name": "androidNativeArm32SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "android_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativearm32/1.7.3/kotlinx-coroutines-core-androidnativearm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativearm32", "version": "1.7.3"}}, {"name": "androidNativeArm64ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "android_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativearm64/1.7.3/kotlinx-coroutines-core-androidnativearm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativearm64", "version": "1.7.3"}}, {"name": "androidNativeArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "android_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativearm64/1.7.3/kotlinx-coroutines-core-androidnativearm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativearm64", "version": "1.7.3"}}, {"name": "androidNativeX64ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "android_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativex64/1.7.3/kotlinx-coroutines-core-androidnativex64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativex64", "version": "1.7.3"}}, {"name": "androidNativeX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "android_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativex64/1.7.3/kotlinx-coroutines-core-androidnativex64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativex64", "version": "1.7.3"}}, {"name": "androidNativeX86ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "android_x86", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativex86/1.7.3/kotlinx-coroutines-core-androidnativex86-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativex86", "version": "1.7.3"}}, {"name": "androidNativeX86SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "android_x86", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-androidnativex86/1.7.3/kotlinx-coroutines-core-androidnativex86-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-androidnativex86", "version": "1.7.3"}}, {"name": "iosArm32ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "ios_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm32/1.7.3/kotlinx-coroutines-core-iosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm32", "version": "1.7.3"}}, {"name": "iosArm32MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "ios_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm32/1.7.3/kotlinx-coroutines-core-iosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm32", "version": "1.7.3"}}, {"name": "iosArm32SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "ios_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm32/1.7.3/kotlinx-coroutines-core-iosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm32", "version": "1.7.3"}}, {"name": "iosArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "ios_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm64/1.7.3/kotlinx-coroutines-core-iosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm64", "version": "1.7.3"}}, {"name": "iosArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "ios_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm64/1.7.3/kotlinx-coroutines-core-iosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm64", "version": "1.7.3"}}, {"name": "iosArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "ios_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosarm64/1.7.3/kotlinx-coroutines-core-iosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosarm64", "version": "1.7.3"}}, {"name": "iosSimulatorArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "ios_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iossimulatorarm64/1.7.3/kotlinx-coroutines-core-iossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iossimulatorarm64", "version": "1.7.3"}}, {"name": "iosSimulatorArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "ios_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iossimulatorarm64/1.7.3/kotlinx-coroutines-core-iossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iossimulatorarm64", "version": "1.7.3"}}, {"name": "iosSimulatorArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "ios_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iossimulatorarm64/1.7.3/kotlinx-coroutines-core-iossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iossimulatorarm64", "version": "1.7.3"}}, {"name": "iosX64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "ios_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosx64/1.7.3/kotlinx-coroutines-core-iosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosx64", "version": "1.7.3"}}, {"name": "iosX64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "ios_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosx64/1.7.3/kotlinx-coroutines-core-iosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosx64", "version": "1.7.3"}}, {"name": "iosX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "ios_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-iosx64/1.7.3/kotlinx-coroutines-core-iosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-iosx64", "version": "1.7.3"}}, {"name": "jsLegacyApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.js.compiler": "legacy", "org.jetbrains.kotlin.platform.type": "js"}, "available-at": {"url": "../../kotlinx-coroutines-core-js/1.7.3/kotlinx-coroutines-core-js-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-js", "version": "1.7.3"}}, {"name": "jsLegacyRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.js.compiler": "legacy", "org.jetbrains.kotlin.platform.type": "js"}, "available-at": {"url": "../../kotlinx-coroutines-core-js/1.7.3/kotlinx-coroutines-core-js-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-js", "version": "1.7.3"}}, {"name": "jsIrApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.js.compiler": "ir", "org.jetbrains.kotlin.platform.type": "js"}, "available-at": {"url": "../../kotlinx-coroutines-core-js/1.7.3/kotlinx-coroutines-core-js-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-js", "version": "1.7.3"}}, {"name": "jsIrRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.js.compiler": "ir", "org.jetbrains.kotlin.platform.type": "js"}, "available-at": {"url": "../../kotlinx-coroutines-core-js/1.7.3/kotlinx-coroutines-core-js-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-js", "version": "1.7.3"}}, {"name": "jsLegacySourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.js.compiler": "legacy", "org.jetbrains.kotlin.platform.type": "js"}, "available-at": {"url": "../../kotlinx-coroutines-core-js/1.7.3/kotlinx-coroutines-core-js-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-js", "version": "1.7.3"}}, {"name": "jvmApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../kotlinx-coroutines-core-jvm/1.7.3/kotlinx-coroutines-core-jvm-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-jvm", "version": "1.7.3"}}, {"name": "jvmRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../kotlinx-coroutines-core-jvm/1.7.3/kotlinx-coroutines-core-jvm-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-jvm", "version": "1.7.3"}}, {"name": "jvmSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../kotlinx-coroutines-core-jvm/1.7.3/kotlinx-coroutines-core-jvm-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-jvm", "version": "1.7.3"}}, {"name": "linuxArm64ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "linux_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-linuxarm64/1.7.3/kotlinx-coroutines-core-linuxarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-linuxarm64", "version": "1.7.3"}}, {"name": "linuxArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "linux_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-linuxarm64/1.7.3/kotlinx-coroutines-core-linuxarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-linuxarm64", "version": "1.7.3"}}, {"name": "linuxX64ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "linux_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-linuxx64/1.7.3/kotlinx-coroutines-core-linuxx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-linuxx64", "version": "1.7.3"}}, {"name": "linuxX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "linux_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-linuxx64/1.7.3/kotlinx-coroutines-core-linuxx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-linuxx64", "version": "1.7.3"}}, {"name": "macosArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "macos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosarm64/1.7.3/kotlinx-coroutines-core-macosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosarm64", "version": "1.7.3"}}, {"name": "macosArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "macos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosarm64/1.7.3/kotlinx-coroutines-core-macosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosarm64", "version": "1.7.3"}}, {"name": "macosArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "macos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosarm64/1.7.3/kotlinx-coroutines-core-macosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosarm64", "version": "1.7.3"}}, {"name": "macosX64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "macos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosx64/1.7.3/kotlinx-coroutines-core-macosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosx64", "version": "1.7.3"}}, {"name": "macosX64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "macos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosx64/1.7.3/kotlinx-coroutines-core-macosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosx64", "version": "1.7.3"}}, {"name": "macosX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "macos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-macosx64/1.7.3/kotlinx-coroutines-core-macosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-macosx64", "version": "1.7.3"}}, {"name": "mingwX64ApiElements-published", "attributes": {"artifactType": "org.jetbrains.kotlin.klib", "org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "mingw_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-mingwx64/1.7.3/kotlinx-coroutines-core-mingwx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-mingwx64", "version": "1.7.3"}}, {"name": "mingwX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "mingw_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-mingwx64/1.7.3/kotlinx-coroutines-core-mingwx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-mingwx64", "version": "1.7.3"}}, {"name": "tvosArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "tvos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosarm64/1.7.3/kotlinx-coroutines-core-tvosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosarm64", "version": "1.7.3"}}, {"name": "tvosArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "tvos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosarm64/1.7.3/kotlinx-coroutines-core-tvosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosarm64", "version": "1.7.3"}}, {"name": "tvosArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "tvos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosarm64/1.7.3/kotlinx-coroutines-core-tvosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosarm64", "version": "1.7.3"}}, {"name": "tvosSimulatorArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "tvos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvossimulatorarm64/1.7.3/kotlinx-coroutines-core-tvossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvossimulatorarm64", "version": "1.7.3"}}, {"name": "tvosSimulatorArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "tvos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvossimulatorarm64/1.7.3/kotlinx-coroutines-core-tvossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvossimulatorarm64", "version": "1.7.3"}}, {"name": "tvosSimulatorArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "tvos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvossimulatorarm64/1.7.3/kotlinx-coroutines-core-tvossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvossimulatorarm64", "version": "1.7.3"}}, {"name": "tvosX64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "tvos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosx64/1.7.3/kotlinx-coroutines-core-tvosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosx64", "version": "1.7.3"}}, {"name": "tvosX64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "tvos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosx64/1.7.3/kotlinx-coroutines-core-tvosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosx64", "version": "1.7.3"}}, {"name": "tvosX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "tvos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-tvosx64/1.7.3/kotlinx-coroutines-core-tvosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-tvosx64", "version": "1.7.3"}}, {"name": "watchosArm32ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm32/1.7.3/kotlinx-coroutines-core-watchosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm32", "version": "1.7.3"}}, {"name": "watchosArm32MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm32/1.7.3/kotlinx-coroutines-core-watchosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm32", "version": "1.7.3"}}, {"name": "watchosArm32SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_arm32", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm32/1.7.3/kotlinx-coroutines-core-watchosarm32-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm32", "version": "1.7.3"}}, {"name": "watchosArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm64/1.7.3/kotlinx-coroutines-core-watchosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm64", "version": "1.7.3"}}, {"name": "watchosArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm64/1.7.3/kotlinx-coroutines-core-watchosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm64", "version": "1.7.3"}}, {"name": "watchosArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosarm64/1.7.3/kotlinx-coroutines-core-watchosarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosarm64", "version": "1.7.3"}}, {"name": "watchosDeviceArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_device_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosdevicearm64/1.7.3/kotlinx-coroutines-core-watchosdevicearm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosdevicearm64", "version": "1.7.3"}}, {"name": "watchosDeviceArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_device_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosdevicearm64/1.7.3/kotlinx-coroutines-core-watchosdevicearm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosdevicearm64", "version": "1.7.3"}}, {"name": "watchosDeviceArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_device_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosdevicearm64/1.7.3/kotlinx-coroutines-core-watchosdevicearm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosdevicearm64", "version": "1.7.3"}}, {"name": "watchosSimulatorArm64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchossimulatorarm64/1.7.3/kotlinx-coroutines-core-watchossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchossimulatorarm64", "version": "1.7.3"}}, {"name": "watchosSimulatorArm64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchossimulatorarm64/1.7.3/kotlinx-coroutines-core-watchossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchossimulatorarm64", "version": "1.7.3"}}, {"name": "watchosSimulatorArm64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_simulator_arm64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchossimulatorarm64/1.7.3/kotlinx-coroutines-core-watchossimulatorarm64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchossimulatorarm64", "version": "1.7.3"}}, {"name": "watchosX64ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx64/1.7.3/kotlinx-coroutines-core-watchosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx64", "version": "1.7.3"}}, {"name": "watchosX64MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx64/1.7.3/kotlinx-coroutines-core-watchosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx64", "version": "1.7.3"}}, {"name": "watchosX64SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_x64", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx64/1.7.3/kotlinx-coroutines-core-watchosx64-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx64", "version": "1.7.3"}}, {"name": "watchosX86ApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-api", "org.jetbrains.kotlin.native.target": "watchos_x86", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx86/1.7.3/kotlinx-coroutines-core-watchosx86-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx86", "version": "1.7.3"}}, {"name": "watchosX86MetadataElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.native.target": "watchos_x86", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx86/1.7.3/kotlinx-coroutines-core-watchosx86-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx86", "version": "1.7.3"}}, {"name": "watchosX86SourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.native.target": "watchos_x86", "org.jetbrains.kotlin.platform.type": "native"}, "available-at": {"url": "../../kotlinx-coroutines-core-watchosx86/1.7.3/kotlinx-coroutines-core-watchosx86-1.7.3.module", "group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core-watchosx86", "version": "1.7.3"}}]}