{"formatVersion": "1.1", "component": {"group": "androidx.profileinstaller", "module": "profileinstaller", "version": "1.3.1", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.0"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "androidx.startup", "module": "startup-runtime", "version": {"requires": "1.1.1"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "1.0"}}], "files": [{"name": "profileinstaller-1.3.1.aar", "url": "profileinstaller-1.3.1.aar", "size": 47619, "sha512": "5cef9df97d0b614b5ef4c2fea2ce72941b40a4d371aab2225d4807ffd482b468d0ccae3ef008332dae18aedace819d0430ba59369e2a18de1259b2270e300f08", "sha256": "d0e402ec31f24028a1dc7eb6a0a3f9d9635c1459392cd734396343b73d673948", "sha1": "c0bac819b4f88a648011542273373d1e5093e3a7", "md5": "5d6c8ef0c1333476fb35b4486a2f5250"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.2.0"}}, {"group": "androidx.concurrent", "module": "concurrent-futures", "version": {"requires": "1.1.0"}}, {"group": "androidx.startup", "module": "startup-runtime", "version": {"requires": "1.1.1"}}, {"group": "com.google.guava", "module": "listenablefuture", "version": {"requires": "1.0"}}], "files": [{"name": "profileinstaller-1.3.1.aar", "url": "profileinstaller-1.3.1.aar", "size": 47619, "sha512": "5cef9df97d0b614b5ef4c2fea2ce72941b40a4d371aab2225d4807ffd482b468d0ccae3ef008332dae18aedace819d0430ba59369e2a18de1259b2270e300f08", "sha256": "d0e402ec31f24028a1dc7eb6a0a3f9d9635c1459392cd734396343b73d673948", "sha1": "c0bac819b4f88a648011542273373d1e5093e3a7", "md5": "5d6c8ef0c1333476fb35b4486a2f5250"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "profileinstaller-1.3.1-sources.jar", "url": "profileinstaller-1.3.1-sources.jar", "size": 38842, "sha512": "5b927c41d47540becbe6cf68f8aa08a656f664b877bbb5f7efbf58f151d58872ea2b7b313325a6550398caa762254108470d7c7715c4f02e6bafac5ea8e61971", "sha256": "0ff5f58d8df11e5f8254013b04386789bdb1367288e9eb74433198261fb14d95", "sha1": "331c5e438d1bdcda02ef158a0151813b44259274", "md5": "b032ce310b9af58d02df9cba69571feb"}]}]}