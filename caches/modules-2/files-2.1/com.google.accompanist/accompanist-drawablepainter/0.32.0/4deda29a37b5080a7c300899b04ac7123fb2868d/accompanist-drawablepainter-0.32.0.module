{"formatVersion": "1.1", "component": {"group": "com.google.accompanist", "module": "accompanist-drawablepainter", "version": "0.32.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.1.1"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.0"}}], "files": [{"name": "accompanist-drawablepainter-0.32.0.aar", "url": "accompanist-drawablepainter-0.32.0.aar", "size": 12592, "sha512": "486056a03b31d3d9e43ad1dd6054f65bc5e7423b007c090fceb2de3a64c6c49851d77784fb146648c458c573e2172d1a846af68c48a4e98fb39acfbc7385d6fb", "sha256": "680d27950f91ba186aa14522ad019dad92c702248d110ebdd6c497734abd7da0", "sha1": "95ccec9539314044fdff3aefac17a2983b53c340", "md5": "ea3a575ca6f1e06d15bc548fcccd780a"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.5.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-android", "version": {"requires": "1.6.4"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.0"}}], "files": [{"name": "accompanist-drawablepainter-0.32.0.aar", "url": "accompanist-drawablepainter-0.32.0.aar", "size": 12592, "sha512": "486056a03b31d3d9e43ad1dd6054f65bc5e7423b007c090fceb2de3a64c6c49851d77784fb146648c458c573e2172d1a846af68c48a4e98fb39acfbc7385d6fb", "sha256": "680d27950f91ba186aa14522ad019dad92c702248d110ebdd6c497734abd7da0", "sha1": "95ccec9539314044fdff3aefac17a2983b53c340", "md5": "ea3a575ca6f1e06d15bc548fcccd780a"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "accompanist-drawablepainter-0.32.0-sources.jar", "url": "accompanist-drawablepainter-0.32.0-sources.jar", "size": 2951, "sha512": "58bf6d699bb6c494e45e1f86399d638e5566dcdff30c3ed720ec09a7f8e931180eeb7e95155213deaf604b0eaed010ab3cf9212dd065e736c0c15030143c46ae", "sha256": "cca65f9e1556f07180e2b16e654fa8ff428780285e7b4911879bf06033520d69", "sha1": "f5eac080d7edbba595d84962b2ab325b41c2e03a", "md5": "b2600c82fd947d178275d2b3613c677b"}]}, {"name": "releaseVariantReleaseJavaDocPublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "accompanist-drawablepainter-0.32.0-javadoc.jar", "url": "accompanist-drawablepainter-0.32.0-javadoc.jar", "size": 269292, "sha512": "ea70c3bf152e628c6860ae1af8a84316a06a2ff2defc4ed177bcea6d5f2cfba302837a7544dd32c817d6ea70760d52b31d4daf3ba637ca537418f0c244d47cda", "sha256": "dff8e3d943dd3c5049209e98f195ad8d69f37a5d6f24f164e0a4197e3ff3dadf", "sha1": "e9b85dd2119bf618d77d236a2cc647fec3a3b45e", "md5": "a016464d919f0a16c139700bd7e21806"}]}]}