<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>radle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.accompanist</groupId>
  <artifactId>accompanist-drawablepainter</artifactId>
  <version>0.32.0</version>
  <packaging>aar</packaging>
  <name>Accompanist Drawable Painter library</name>
  <description>Utilities for Jetpack Compose</description>
  <url>https://github.com/google/accompanist/</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>google</id>
      <name>Google</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/google/accompanist.git</connection>
    <developerConnection>scm:git:git://github.com/google/accompanist.git</developerConnection>
    <url>https://github.com/google/accompanist/</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>androidx.compose.ui</groupId>
      <artifactId>ui</artifactId>
      <version>1.5.0</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-android</artifactId>
      <version>1.6.4</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
