{"formatVersion": "1.1", "component": {"group": "com.android.tools.build", "module": "gradle", "version": "8.7.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.9"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.android.tools.build", "module": "builder", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.build", "module": "builder-model", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.build", "module": "gradle-api", "version": {"requires": "8.7.3"}}], "files": [{"name": "gradle-8.7.3.jar", "url": "gradle-8.7.3.jar", "size": 12429787, "sha512": "ceff85cb665966cfc2d07f33e594cf4fa47efa7e7b396cab68f6cda75539ac412db54d15e4529f308f46fb515fb9b98954e0e4179bd6599b2c22cb9dda8be3f1", "sha256": "abb4f9e92c9838bd9989ba05968ec892189e16e23f42adba2e063843da04070d", "sha1": "557c0eaed243df7b293d8b1f2c252d97fcc71dda", "md5": "cd4b3f18df99f5aec6be9089b4a8e09a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.android.tools.build", "module": "gradle-settings-api", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools", "module": "sdk-common", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools", "module": "sdklib", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools", "module": "repository", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.ddms", "module": "ddmlib", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.build", "module": "aapt2-proto", "version": {"requires": "8.7.3-12006047"}}, {"group": "com.android.tools.build", "module": "aaptcompiler", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.analytics-library", "module": "crash", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.analytics-library", "module": "shared", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.lint", "module": "lint-model", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.lint", "module": "lint-typedef-remover", "version": {"requires": "31.7.3"}}, {"group": "androidx.databinding", "module": "databinding-compiler-common", "version": {"requires": "8.7.3"}}, {"group": "androidx.databinding", "module": "databinding-common", "version": {"requires": "8.7.3"}}, {"group": "com.android.databinding", "module": "baseLibrary", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.build", "module": "builder-test-api", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.layoutlib", "module": "layoutlib-api", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-device-provider-ddmlib-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-device-provider-gradle-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-device-provider-profile-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-additional-test-output-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-coverage-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-emulator-control-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-logcat-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-apk-installer-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-host-retention-proto", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools.utp", "module": "android-test-plugin-result-listener-gradle-proto", "version": {"requires": "31.7.3"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.20"}}, {"group": "com.android.tools.build", "module": "transform-api", "version": {"requires": "2.0.0-deprecated-use-gradle-api"}}, {"group": "org.apache.httpcomponents", "module": "httpmime", "version": {"requires": "4.5.6"}}, {"group": "commons-io", "module": "commons-io", "version": {"requires": "2.13.0"}}, {"group": "org.ow2.asm", "module": "asm", "version": {"requires": "9.6"}}, {"group": "org.ow2.asm", "module": "asm-analysis", "version": {"requires": "9.6"}}, {"group": "org.ow2.asm", "module": "asm-commons", "version": {"requires": "9.6"}}, {"group": "org.ow2.asm", "module": "asm-util", "version": {"requires": "9.6"}}, {"group": "org.bouncycastle", "module": "bcpkix-jdk18on", "version": {"requires": "1.77"}}, {"group": "org.glassfish.jaxb", "module": "jaxb-runtime", "version": {"requires": "2.3.2"}}, {"group": "net.sf.jopt-simple", "module": "jopt-simple", "version": {"requires": "4.9"}}, {"group": "com.android.tools.build", "module": "bundletool", "version": {"requires": "1.17.1"}}, {"group": "com.android.tools.build.jetifier", "module": "jetifier-core", "version": {"requires": "1.0.0-beta10"}}, {"group": "com.android.tools.build.jetifier", "module": "jetifier-processor", "version": {"requires": "1.0.0-beta10"}}, {"group": "com.squareup", "module": "javapoet", "version": {"requires": "1.10.0"}}, {"group": "com.google.protobuf", "module": "protobuf-java", "version": {"requires": "3.22.3"}}, {"group": "com.google.protobuf", "module": "protobuf-java-util", "version": {"requires": "3.22.3"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.10.1"}}, {"group": "io.grpc", "module": "grpc-core", "version": {"requires": "1.57.0"}}, {"group": "io.grpc", "module": "grpc-netty", "version": {"requires": "1.57.0"}}, {"group": "io.grpc", "module": "grpc-protobuf", "version": {"requires": "1.57.0"}}, {"group": "io.grpc", "module": "grpc-stub", "version": {"requires": "1.57.0"}}, {"group": "com.google.crypto.tink", "module": "tink", "version": {"requires": "1.7.0"}}, {"group": "com.google.testing.platform", "module": "core-proto", "version": {"requires": "0.0.9-alpha02"}}, {"group": "com.google.flatbuffers", "module": "flatbuffers-java", "version": {"requires": "1.12.0"}}, {"group": "org.tensorflow", "module": "tensorflow-lite-metadata", "version": {"requires": "0.1.0-rc2"}}, {"group": "com.android.tools.build", "module": "builder", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.build", "module": "builder-model", "version": {"requires": "8.7.3"}}, {"group": "com.android.tools.build", "module": "gradle-api", "version": {"requires": "8.7.3"}}], "files": [{"name": "gradle-8.7.3.jar", "url": "gradle-8.7.3.jar", "size": 12429787, "sha512": "ceff85cb665966cfc2d07f33e594cf4fa47efa7e7b396cab68f6cda75539ac412db54d15e4529f308f46fb515fb9b98954e0e4179bd6599b2c22cb9dda8be3f1", "sha256": "abb4f9e92c9838bd9989ba05968ec892189e16e23f42adba2e063843da04070d", "sha1": "557c0eaed243df7b293d8b1f2c252d97fcc71dda", "md5": "cd4b3f18df99f5aec6be9089b4a8e09a"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "gradle-8.7.3-javadoc.jar", "url": "gradle-8.7.3-javadoc.jar", "size": 633220, "sha512": "1d4a699ab2068cfbe7ef2b1110c184fb6d701d88c95e5b4f589ba8dddf2f8f9c3821fbed51e34bd058aecef2125cde52c9cd285f407a093ae30736a5fe5ee964", "sha256": "efc409e40c43076a4eb04bb8cbcae3ac3e111f2a7261c14f07eed9c164a3f625", "sha1": "736e7a429569aa2781c8f3faf59020b6b45d669b", "md5": "678279481ec4fa34df50bb811c39454b"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "gradle-8.7.3-sources.jar", "url": "gradle-8.7.3-sources.jar", "size": 3785344, "sha512": "d444c462a7f9e017599c73f69431796c7df878341c5423814928bdbc8df70012f689e25270a0b896bd3b0631e97e408709a2d2af7df91d79284b1bcce9969789", "sha256": "dc67553aecd5b14487d290f6f45b33f9a9fc5872b3f0f06231442769585a841f", "sha1": "0ef82437a9d289d2d0a87782c67f8bead0edb499", "md5": "c59fcfdd3cc3f14aa125942df265b93c"}]}]}