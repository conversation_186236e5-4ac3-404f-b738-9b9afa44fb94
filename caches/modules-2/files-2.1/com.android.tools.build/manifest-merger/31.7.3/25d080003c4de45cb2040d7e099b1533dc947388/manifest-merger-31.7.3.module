{"formatVersion": "1.1", "component": {"group": "com.android.tools.build", "module": "manifest-merger", "version": "31.7.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.9"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "files": [{"name": "manifest-merger-31.7.3.jar", "url": "manifest-merger-31.7.3.jar", "size": 256510, "sha512": "79689eb0b86e8b9afa15da2239b762d1716d141c2eea297fcb8689970f9df2efffded6e5137432585e90281f5dbc054fedce7c067ad9ca275cae8f0cd9b46396", "sha256": "f1a6e56adbd557d6bf34447a62ff4680642c799c397a4bc14a81256bd961e6ab", "sha1": "0d0489e76e4eb785336317add6b65d57b928a429", "md5": "85fe16a0578785f635a9691820ccc0b7"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.android.tools", "module": "common", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools", "module": "sdklib", "version": {"requires": "31.7.3"}}, {"group": "com.android.tools", "module": "sdk-common", "version": {"requires": "31.7.3"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.10.1"}}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk8", "version": {"requires": "1.9.20"}}], "files": [{"name": "manifest-merger-31.7.3.jar", "url": "manifest-merger-31.7.3.jar", "size": 256510, "sha512": "79689eb0b86e8b9afa15da2239b762d1716d141c2eea297fcb8689970f9df2efffded6e5137432585e90281f5dbc054fedce7c067ad9ca275cae8f0cd9b46396", "sha256": "f1a6e56adbd557d6bf34447a62ff4680642c799c397a4bc14a81256bd961e6ab", "sha1": "0d0489e76e4eb785336317add6b65d57b928a429", "md5": "85fe16a0578785f635a9691820ccc0b7"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "manifest-merger-31.7.3-javadoc.jar", "url": "manifest-merger-31.7.3-javadoc.jar", "size": 275849, "sha512": "360c856f7996b5a40fc538d3f489a86c3dd7836b8f6b018862974ce96fe3e50ef2b4fdd8054673aaa5ebdabbce60d014166a6e0d0287e8ec598be061ffb03cba", "sha256": "2028f395694eb0ecf63a53f7ccb6f5d4a76e7ad25ad69bb9554f0f253a055cf8", "sha1": "ce91e245080e41c8671ef6eeb2ee3938f9385cb9", "md5": "5399afc99303c75c43fc60ba10b8fc80"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "manifest-merger-31.7.3-sources.jar", "url": "manifest-merger-31.7.3-sources.jar", "size": 235001, "sha512": "cdfde8ad4de1925661e6d4292f41528a994c20967a6ab67b078d578a5944c3d87fb48823c634ef06b07a4e225b1ac76ff41fa0bf32c482dca263ae7b05770a39", "sha256": "05efa1bb3ac0723f82342bd727af2aa28c34f36363b478c67ca64a6cf6f0d642", "sha1": "2487af6eee43a04f77cd004e43576ca14bb1ee9e", "md5": "0cac99084d48d2b2e535ede009fd7fa9"}]}]}