<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.android.tools.build</groupId>
  <artifactId>builder</artifactId>
  <version>8.7.3</version>
  <dependencies>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>manifest-merger</artifactId>
      <version>31.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android</groupId>
      <artifactId>zipflinger</artifactId>
      <version>8.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>apksig</artifactId>
      <version>8.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>apkzlib</artifactId>
      <version>8.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup</groupId>
      <artifactId>javawriter</artifactId>
      <version>2.5.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>builder-model</artifactId>
      <version>8.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.build</groupId>
      <artifactId>builder-test-api</artifactId>
      <version>8.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>sdklib</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>sdk-common</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>common</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.ddms</groupId>
      <artifactId>ddmlib</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android</groupId>
      <artifactId>signflinger</artifactId>
      <version>8.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>protos</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>tracker</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.layoutlib</groupId>
      <artifactId>layoutlib-api</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.20</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk18on</artifactId>
      <version>1.77</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.10</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
      <version>1.77</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>javax.inject</groupId>
      <artifactId>javax.inject</artifactId>
      <version>1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-commons</artifactId>
      <version>9.6</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
  <description>Library to build Android applications.</description>
  <url>https://developer.android.com/studio/build</url>
  <name>com.android.tools.build.builder</name>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <scm>
    <connection>git://android.googlesource.com/platform/tools/base.git</connection>
    <url>https://android.googlesource.com/platform/tools/base</url>
  </scm>
</project>
