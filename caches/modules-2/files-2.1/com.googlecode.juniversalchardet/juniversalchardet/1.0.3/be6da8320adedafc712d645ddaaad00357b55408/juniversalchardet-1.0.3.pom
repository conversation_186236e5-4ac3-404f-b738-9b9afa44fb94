<?xml version="1.0" encoding="UTF-8"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.googlecode.juniversalchardet</groupId>
	<artifactId>juniversalchardet</artifactId>
	<version>1.0.3</version>
	<packaging>jar</packaging>
	<name>juniversalchardet</name>
	<description>Java port of universalchardet</description>
	<url>http://juniversalchardet.googlecode.com/</url>
	<licenses>
		<license>
			<name>Mozilla Public License 1.1 (MPL 1.1)</name>
			<url>http://www.mozilla.org/MPL/MPL-1.1.html</url>
			<distribution>repo</distribution>
		</license>
	</licenses>
	<scm>
		<connection>scm:svn:http://juniversalchardet.googlecode.com/svn/trunk/</connection>
		<url>http://code.google.com/p/juniversalchardet/source/browse/</url>
	</scm>
	<developers>
		<developer>
			<id>takscape</id>
			<email><EMAIL></email>
		</developer>
	</developers>
	<build>
		<sourceDirectory>${basedir}/src</sourceDirectory>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>2.3.2</version>
				<configuration>
					<source>1.5</source>
					<target>1.5</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<properties>
		<project.build.sourceEncoding>us-ascii</project.build.sourceEncoding>
	</properties>
</project>