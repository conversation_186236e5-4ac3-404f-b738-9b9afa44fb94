<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright 2022 Google LLC

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <name>Tink Cryptography API</name>
  <description>Tink is a small cryptographic library that provides a safe, simple, agile and fast way to accomplish some common cryptographic tasks.
  </description>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>7</version>
  </parent>

  <groupId>com.google.crypto.tink</groupId>
  <artifactId>tink</artifactId>
  <version>1.7.0</version>
  <packaging>jar</packaging>
  <url>http://github.com/google/tink</url>

  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <distributionManagement>
    <snapshotRepository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <repository>
      <id>ossrh</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
    </repository>
  </distributionManagement>

  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/google/tink/issues</url>
  </issueManagement>

  <mailingLists>
    <mailingList>
      <name>tink-users</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/group/tink-users</archive>
    </mailingList>
  </mailingLists>

  <developers>
    <developer>
      <organization>Google Inc.</organization>
      <organizationUrl>https://www.google.com</organizationUrl>
    </developer>
  </developers>

  <scm>
    <connection>scm:git:**************:google/tink.git</connection>
    <developerConnection>scm:git:**************:google/tink.git</developerConnection>
    <url>https://github.com/google/tink.git</url>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <java.version>1.8</java.version>
    <gson.version>2.8.9</gson.version>
    <protobuf.version>3.19.3</protobuf.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>${protobuf.version}</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>${gson.version}</version>
    </dependency>
  </dependencies>
</project>
