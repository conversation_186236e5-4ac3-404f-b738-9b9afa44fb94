<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.android.tools.analytics-library</groupId>
  <artifactId>tracker</artifactId>
  <version>31.7.3</version>
  <name>Android Tools Analytics Tracker</name>
  <description>Library for tracking usage analytics.</description>
  <url>http://tools.android.com/</url>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Project</name>
    </developer>
  </developers>
  <dependencies>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>protos</artifactId>
      <version>31.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools.analytics-library</groupId>
      <artifactId>shared</artifactId>
      <version>31.7.3</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>annotations</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.android.tools</groupId>
      <artifactId>common</artifactId>
      <version>31.7.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.0.1-jre</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.google.protobuf</groupId>
      <artifactId>protobuf-java</artifactId>
      <version>3.22.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
      <version>1.9.20</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>
</project>
