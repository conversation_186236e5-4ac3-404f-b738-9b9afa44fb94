{"formatVersion": "1.1", "component": {"group": "androidx.compose.animation", "module": "animation", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-1.6.8-multiplatform-sources.jar", "url": "animation-1.6.8-multiplatform-sources.jar", "size": 48507, "sha512": "c92961b4a3c646b2791b15ba8ff2a5bc62b536e7cb1777f8e9ae377ee6f406213093c3664aea4ec6a345a50c8f8a7e20f37d645a5549bc1a2d714a4a0c2f77f2", "sha256": "ebe691185a8933bab3866e60e236689a81aef82048db80f9b86a9f38264360a6", "sha1": "9b3f9a6c89db86231a11f4fce966444e3b9fa034", "md5": "6b05a214047f672a4ab040fee0ff83d5"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "apiLevels.json", "url": "animation-1.6.8-versionMetadata.json", "size": 25190, "sha512": "db0e4b9b6e32ad2974724536c07b1305ead9149e3c3b26596ea6af93a1bec37fd33d4a467abb49c6a92c3d9ba86aedf80f0b8e60362e3b90806abcce39256ac4", "sha256": "4bc1aa01e2864219555a562c72642f2f842393ea62acb3940abcaad3428bc06e", "sha1": "3a80bd77c7498afea8d2711b31a40ac2f7197f40", "md5": "92060113604eee744895b5c96b0f0845"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-metadata-1.6.8.jar", "url": "animation-1.6.8.jar", "size": 758, "sha512": "b87e59af85e728d301976cbcca52430286ed2c0df6d852cdf7ea001bac873e7d358279a7e1209965755daf6c5e19aa0b3e6c8e0421369939b4f16f657c36daf2", "sha256": "a51a58d4a9f2a5a1177c25ed0cab927fe525caa2cf458514dcdff7033ab90555", "sha1": "746ccef48b368174eef742546af6cace3ea24e42", "md5": "689da4e64a1661a1c64ac57652fe4854"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-kotlin-1.6.8-sources.jar", "url": "animation-1.6.8-sources.jar", "size": 43595, "sha512": "350d5bbc6cff7793837e1148d244798302a1de2a32a722d77fc77204b220e280bb9bdbcc8b31c5ad120ceb955fe9c7785f085b070b0d84b580fedd54abcf1f4f", "sha256": "ae9e7f96ea50d246cd738d80873007bb1ae7ff1f3e5fddd7e70f2ee195d32682", "sha1": "6cd8242ba28b2d18bacace503d7a414e9694e576", "md5": "8de7070f3ad00a29ed056a9bc402b56b"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-android/1.6.8/animation-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-android/1.6.8/animation-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-android/1.6.8/animation-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-desktop/1.6.8/animation-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-desktop/1.6.8/animation-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-desktop/1.6.8/animation-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-desktop", "version": "1.6.8"}}]}