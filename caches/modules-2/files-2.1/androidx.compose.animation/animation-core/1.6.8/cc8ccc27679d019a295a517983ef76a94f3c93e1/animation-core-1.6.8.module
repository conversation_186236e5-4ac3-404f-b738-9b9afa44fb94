{"formatVersion": "1.1", "component": {"group": "androidx.compose.animation", "module": "animation-core", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "androidxSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "androidx-multiplatform-docs"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-1.6.8-multiplatform-sources.jar", "url": "animation-core-1.6.8-multiplatform-sources.jar", "size": 88157, "sha512": "15196ffe888d09efc93a991e5c0d6f75432fed6f679015e6f64ad0bc189dbdf74b54c311014cf29aadb69cc5368189da01a1c27145f3cfa3ceedbd76e5f82d9e", "sha256": "8deb81fd23d516c1207740b5a2d6d00dbf856a15952b30d5b87f1bf52f6e1350", "sha1": "5c5a1e8e6318500cb405d6760a3fe7d0c1060bcd", "md5": "cfcf365a6cb433809524cba34e4b9606"}]}, {"name": "libraryVersionMetadata", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.usage": "library-version-metadata"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "apiLevels.json", "url": "animation-core-1.6.8-versionMetadata.json", "size": 49695, "sha512": "dbdeb3833ea203fc72b7ee8ee5e90aa8f2d280c701729c732cf1b667cd5154f2c2f44f1220568e2891bcbfb5cb1f014db810c51ff19d973e9131700592a32a81", "sha256": "37165f95308e22034bd0e053f66bca99be98fcf4ba7aef55a577b4dd1d916fd8", "sha1": "daa22dfea2a92c4502781ca8b7375592596d7eb2", "md5": "e188d97eb04fbb70b45fb7307982a7e8"}]}, {"name": "metadataApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "kotlin-metadata", "org.jetbrains.kotlin.platform.type": "common"}, "dependencies": [{"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-metadata-1.6.8.jar", "url": "animation-core-1.6.8.jar", "size": 723, "sha512": "d6bf93025b5e97ca042ed0a622a7c735c40d03bcd8113a2c2e4463cd42f2a90da92694e681f9f3c7f363012f18fcaf583dfb81d7739efe5a2cb20671e8091417", "sha256": "b628671c9ebefd68ff33b35d2c5ca644bdb4f110c5fa24260930258d4bfef93d", "sha1": "53af16e46e0bd030589b6c7d085f3dbac8fca1a6", "md5": "bab5e2d76e88be349e872e63db91d1c2"}]}, {"name": "metadataSourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "kotlin-runtime", "org.jetbrains.kotlin.platform.type": "common"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-kotlin-1.6.8-sources.jar", "url": "animation-core-1.6.8-sources.jar", "size": 87833, "sha512": "1caa0670000258bc589873db1b771d1c88695719e60eaff399799f3c0fb1662041c88cfb2305ee58aa1a97bfeb36000cab8cc896f6918488eb5be9f92465c879", "sha256": "e6b27691e58225f461bf22f0da990876e0db88dcaa20709a7c5bcf7b9a638273", "sha1": "ce2ed09ecc697f6be25a92c205f17917892a9680", "md5": "0ccabf4c3d143df695e95cb519c949b0"}]}, {"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-core-android/1.6.8/animation-core-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-android", "version": "1.6.8"}}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-core-android/1.6.8/animation-core-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-android", "version": "1.6.8"}}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "available-at": {"url": "../../animation-core-android/1.6.8/animation-core-android-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-android", "version": "1.6.8"}}, {"name": "desktopApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-core-desktop/1.6.8/animation-core-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-desktop", "version": "1.6.8"}}, {"name": "desktopRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-core-desktop/1.6.8/animation-core-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-desktop", "version": "1.6.8"}}, {"name": "desktopSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "available-at": {"url": "../../animation-core-desktop/1.6.8/animation-core-desktop-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core-desktop", "version": "1.6.8"}}]}