{"formatVersion": "1.1", "component": {"url": "../../animation-core/1.6.8/animation-core-1.6.8.module", "group": "androidx.compose.animation", "module": "animation-core", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-release.aar", "url": "animation-core-android-1.6.8.aar", "size": 1463381, "sha512": "6db228dacfd6dbbf4321de61a88fe3ac9595c9b6a007eeb891c2727db5120665a653357466063649f9e09d747965c7f8e688c0b5e6a47edc93762002d4cc7763", "sha256": "4f72b35deaf013ffe4122bf35ceb8a1ce0a45b8557953b353fe9c1a9dab64303", "sha1": "fbd8dd83ae1e9a99de28f2d2843842500d2991a1", "md5": "c93c759e2af77d93df8387511e017a60"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.collection", "module": "collection", "version": {"requires": "1.4.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-unit", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.7.1"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-release.aar", "url": "animation-core-android-1.6.8.aar", "size": 1463381, "sha512": "6db228dacfd6dbbf4321de61a88fe3ac9595c9b6a007eeb891c2727db5120665a653357466063649f9e09d747965c7f8e688c0b5e6a47edc93762002d4cc7763", "sha256": "4f72b35deaf013ffe4122bf35ceb8a1ce0a45b8557953b353fe9c1a9dab64303", "sha1": "fbd8dd83ae1e9a99de28f2d2843842500d2991a1", "md5": "c93c759e2af77d93df8387511e017a60"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation-core is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-core-android-1.6.8-sources.jar", "url": "animation-core-android-1.6.8-sources.jar", "size": 87833, "sha512": "2dfe6eb567d919ba84cbecf72a4ce124b5bbb8e261e8590b156604c203750806e49ce453ee6ef6ae77340e50fff55425dcd25203ac037bdfc3cc40fbd5a3b53f", "sha256": "f95dd58db836a267ecd21acaa84eb160080f481cf8c88c996f4ca34ac84b95e2", "sha1": "850b59ca441485047c2c3cc7d1ba44088757c18b", "md5": "eeea801dccf326ebe0269c5ede1ae932"}]}]}