{"formatVersion": "1.1", "component": {"url": "../../animation/1.6.8/animation-1.6.8.module", "group": "androidx.compose.animation", "module": "animation", "version": "1.6.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.4"}}, "variants": [{"name": "releaseApiElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-release.aar", "url": "animation-android-1.6.8.aar", "size": 1410585, "sha512": "f7f4e364f9bae5339691dc0a92c661a878673578c55d4fd138407cb8a82e2c0a270218e50b0a55be6b1a675f66a347be5f0e973f7d27e3fa8c55be937a936be9", "sha256": "8ba43fe0af09ef805121c8ac0367856dcae1115dade8e9daab3909ce010861e2", "sha1": "9738170c0e7188e9a81f9131f1462b872ac70348", "md5": "e1b11f9ef2257c7419a97e0caee2ff46"}]}, {"name": "releaseRuntimeElements-published", "attributes": {"org.gradle.category": "library", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencies": [{"group": "androidx.annotation", "module": "annotation", "version": {"requires": "1.1.0"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.foundation", "module": "foundation-layout", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.runtime", "module": "runtime", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-geometry", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "androidx.compose.ui", "module": "ui-util", "version": {"requires": "1.6.8"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}, {"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common", "version": {"requires": "1.8.22"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-common"}, {"group": "org.jetbrains.kotlin", "module": "kotlin-test-annotations-common"}]}], "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-release.aar", "url": "animation-android-1.6.8.aar", "size": 1410585, "sha512": "f7f4e364f9bae5339691dc0a92c661a878673578c55d4fd138407cb8a82e2c0a270218e50b0a55be6b1a675f66a347be5f0e973f7d27e3fa8c55be937a936be9", "sha256": "8ba43fe0af09ef805121c8ac0367856dcae1115dade8e9daab3909ce010861e2", "sha1": "9738170c0e7188e9a81f9131f1462b872ac70348", "md5": "e1b11f9ef2257c7419a97e0caee2ff46"}]}, {"name": "releaseSourcesElements-published", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "fake-sources", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "androidJvm"}, "dependencyConstraints": [{"group": "androidx.compose.animation", "module": "animation-core", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-tooling-internal", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}, {"group": "androidx.compose.animation", "module": "animation-graphics", "version": {"requires": "1.6.8"}, "reason": "animation is in atomic group androidx.compose.animation"}], "files": [{"name": "animation-android-1.6.8-sources.jar", "url": "animation-android-1.6.8-sources.jar", "size": 45987, "sha512": "28e4696f6d8e5a68602ce3397acd476d8458de394ec622b67e896782bc293f7edd6a3cecaa5475030cdb568649ba2d5f8bf900968f5422013daaea03e71d3382", "sha256": "d78d4c2cb2797e69a56ea905201b087a15e538a490b783fe687775989a26e9ba", "sha1": "22e0eb3f955b01f383e6f7cda43646b887cc0f37", "md5": "0b6f0c4b8aa90fc858f385a5a3464414"}]}]}