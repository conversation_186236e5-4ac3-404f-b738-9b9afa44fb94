package com.example.recoverdev.ui.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.*
import kotlinx.coroutines.delay
import com.example.recoverdev.R
import com.example.recoverdev.data.model.FileType

@Composable
fun RecoveryAnimationScreen(
    fileType: FileType? = null,
    onAnimationComplete: () -> Unit
) {
    // Lottie animation composition for recovery
    val composition by rememberLottieComposition(LottieCompositionSpec.Asset("anim/recover.json"))
    val progress by animateLottieCompositionAsState(
        composition = composition,
        iterations = LottieConstants.IterateForever
    )
    
    // Auto complete after 3 seconds
    LaunchedEffect(Unit) {
        delay(5000)
        onAnimationComplete()
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize(),
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Lottie Animation area

            Spacer(modifier = Modifier.height(60.dp))

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 40.dp)
                    .background(colorResource(R.color.main_background), RoundedCornerShape(27.dp))
                    .padding(16.dp)
                    .aspectRatio(1f)
            ){
                LottieAnimation(
                    composition = composition,
                    progress = { progress }
                )
            }

            Spacer(modifier = Modifier.height(32.dp))

            val typeRes = when (fileType) {
                FileType.PHOTO -> stringResource(R.string.photos_text)
                FileType.VIDEO -> stringResource(R.string.videos_text)
                FileType.AUDIO -> stringResource(R.string.audios_text)
                FileType.OTHER, null -> stringResource(R.string.files_text)
            }

            // Text information
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = stringResource(R.string.recover_files_text, typeRes),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colorResource(R.color.first_text_black),
                    textAlign = TextAlign.Center
                )
                
                Text(
                    text = stringResource(R.string.just_a_moment),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.Black,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}