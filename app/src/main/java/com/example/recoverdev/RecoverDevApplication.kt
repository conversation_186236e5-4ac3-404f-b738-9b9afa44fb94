package com.example.recoverdev

import android.app.Application
import android.util.Log
import coil.ImageLoader
import coil.ImageLoaderFactory
import coil.decode.VideoFrameDecoder
import com.tencent.mmkv.MMKV
import ads.AdMobInitializer
import ads.AppOpenAdManager

class RecoverDevApplication : Application(), ImageLoaderFactory {
    
    private lateinit var appOpenAdManager: AppOpenAdManager
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize MMKV
        MMKV.initialize(this)
        
        // Initialize AdMob
        AdMobInitializer.initialize(this)
        
        // Initialize AppOpenAdManager
        appOpenAdManager = AppOpenAdManager(this)
        
        // Register activity lifecycle callbacks to track app foreground/background
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            private var startedActivities = 0
            
            override fun onActivityCreated(activity: android.app.Activity, savedInstanceState: android.os.Bundle?) {}
            
            override fun onActivityStarted(activity: android.app.Activity) {
                if (startedActivities == 0) {
                    // App came to foreground
                    appOpenAdManager.onAppForegrounded()
                }
                startedActivities++
            }
            
            override fun onActivityResumed(activity: android.app.Activity) {}
            
            override fun onActivityPaused(activity: android.app.Activity) {}
            
            override fun onActivityStopped(activity: android.app.Activity) {
                startedActivities--
            }
            
            override fun onActivitySaveInstanceState(activity: android.app.Activity, outState: android.os.Bundle) {}
            
            override fun onActivityDestroyed(activity: android.app.Activity) {}
        })
    }
    
    override fun newImageLoader(): ImageLoader {
        return ImageLoader.Builder(this)
            .components {
                add(VideoFrameDecoder.Factory())
            }
            .build()
    }
}