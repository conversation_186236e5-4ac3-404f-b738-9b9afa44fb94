package com.example.recoverdev

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.recoverdev.ui.screen.SplashScreen
import com.example.recoverdev.ui.screen.GuideScreen
import com.example.recoverdev.ui.theme.RecoverDevTheme
import com.example.recoverdev.utils.PreferenceUtils
import com.example.recoverdev.viewmodel.SplashState
import com.example.recoverdev.viewmodel.SplashViewModel

class SplashActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            RecoverDevTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    val splashViewModel: SplashViewModel = viewModel()
                    val state by splashViewModel.state.collectAsState()
                    
                    when (state) {
                        is SplashState.NavigateToGuide -> {
                            GuideScreen(
                                onComplete = {
                                    PreferenceUtils.setGuideShown()
                                    navigateToMainActivity()
                                }
                            )
                        }
                        is SplashState.NavigateToMain -> {
                            LaunchedEffect(Unit) {
                                navigateToMainActivity()
                            }
                        }
                        else -> {
                            SplashScreen(
                                onNavigateToMain = {
                                },
                                splashViewModel = splashViewModel
                            )
                        }
                    }
                }
            }
        }
    }
    
    private fun navigateToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
}