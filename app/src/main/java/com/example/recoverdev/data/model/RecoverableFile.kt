package com.example.recoverdev.data.model

import java.io.File

data class RecoverableFile(
    val id: String,
    val name: String,
    val path: String,
    val size: Long,
    val dateModified: Long,
    val type: FileType,
    val format: String,
    val mimeType: String? = null,
    val isRecovered: Boolean = false,
    val recoveredPath: String? = null,
    val thumbnailPath: String? = null,
    // File objects for direct access
    val file: File? = null,
    val recoveredFile: File? = null,
    // Image-specific properties
    val width: Int? = null,
    val height: Int? = null,
    val resolution: String? = null,
    // Video/Audio-specific properties
    val duration: Long? = null,
    val dimension: String? = null
)

enum class FileType {
    PHOTO, VIDEO, AUDIO, OTHER
}