package ads

import android.content.Context
import android.util.Log
import com.google.android.gms.ads.MobileAds
import com.google.android.gms.ads.RequestConfiguration
import com.google.android.gms.ads.initialization.OnInitializationCompleteListener

object AdMobInitializer {

    private const val TAG = "AdMobInitializer"
    private var isInitialized = false

    fun initialize(context: Context, testDeviceIds: List<String> = emptyList()) {
        if (isInitialized) {
            Log.d(TAG, "AdMob is already initialized")
            return
        }

        // Set test device IDs for testing
        if (testDeviceIds.isNotEmpty()) {
            val requestConfiguration = RequestConfiguration.Builder()
                .setTestDeviceIds(testDeviceIds)
                .build()
            MobileAds.setRequestConfiguration(requestConfiguration)
        }

        MobileAds.initialize(context, object : OnInitializationCompleteListener {
            override fun onInitializationComplete(initializationStatus: com.google.android.gms.ads.initialization.InitializationStatus) {
                isInitialized = true
                Log.d(TAG, "AdMob initialization completed successfully")
                
                // Log adapter statuses
                initializationStatus.adapterStatusMap.forEach { (adapter, status) ->
                    Log.d(TAG, "Adapter: $adapter, Status: ${status.initializationState}, " +
                            "Description: ${status.description}")
                }
            }
        })
    }

    fun isInitialized(): Boolean {
        return isInitialized
    }

    fun setTestDeviceIds(testDeviceIds: List<String>) {
        val requestConfiguration = RequestConfiguration.Builder()
            .setTestDeviceIds(testDeviceIds)
            .build()
        MobileAds.setRequestConfiguration(requestConfiguration)
    }
}