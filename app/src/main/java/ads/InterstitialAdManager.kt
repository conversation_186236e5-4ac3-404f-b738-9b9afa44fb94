package ads

import android.app.Activity
import android.content.Context
import android.util.Log
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.interstitial.InterstitialAd
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback

class InterstitialAdManager(private val context: Context) {

    private var interstitialAd: InterstitialAd? = null
    private var isLoadingAd = false

    // Test ad unit ID - replace with your actual ad unit ID
    private val adUnitId = "ca-app-pub-3940256099942544/1033173712"

    fun loadAd() {
        if (isLoadingAd || isAdAvailable()) {
            return
        }

        isLoadingAd = true
        val request = AdRequest.Builder().build()

        InterstitialAd.load(
            context,
            adUnitId,
            request,
            object : InterstitialAdLoadCallback() {
                override fun onAdLoaded(ad: InterstitialAd) {
                    interstitialAd = ad
                    isLoadingAd = false
                    Log.d(TAG, "Interstitial ad loaded successfully.")
                }

                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    isLoadingAd = false
                    interstitialAd = null
                    Log.d(TAG, "Interstitial ad failed to load: ${loadAdError.message}")
                }
            }
        )
    }

    fun showAd(activity: Activity, onAdDismissed: () -> Unit = {}) {
        if (!isAdAvailable()) {
            Log.d(TAG, "The interstitial ad wasn't ready yet.")
            loadAd()
            return
        }

        interstitialAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
            override fun onAdDismissedFullScreenContent() {
                interstitialAd = null
                loadAd()
                onAdDismissed()
            }

            override fun onAdFailedToShowFullScreenContent(adError: AdError) {
                interstitialAd = null
                loadAd()
            }

            override fun onAdShowedFullScreenContent() {
                // Ad was shown
            }
        }

        interstitialAd?.show(activity)
    }

    fun isAdAvailable(): Boolean {
        return interstitialAd != null
    }

    companion object {
        private const val TAG = "InterstitialAdManager"
    }
}