package ads

import android.content.Context
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.RatingBar
import android.widget.TextView
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.VideoOptions
import com.google.android.gms.ads.nativead.MediaView
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import com.google.android.gms.ads.nativead.NativeAdView

class NativeAdManager(private val context: Context) {

    private var nativeAd: NativeAd? = null
    private var isLoadingAd = false

    // Test ad unit ID - replace with your actual ad unit ID
    private val adUnitId = "ca-app-pub-3940256099942544/2247696110"

    interface NativeAdLoadListener {
        fun onAdLoaded(adView: NativeAdView)
        fun onAdFailedToLoad(error: String)
    }

    fun loadAd(listener: NativeAdLoadListener? = null) {
        if (isLoadingAd || isAdAvailable()) {
            return
        }

        isLoadingAd = true

        val adLoader = AdLoader.Builder(context, adUnitId)
            .forNativeAd { ad ->
                nativeAd = ad
                isLoadingAd = false
                Log.d(TAG, "Native ad loaded successfully.")

                val adView = createNativeAdView()
                populateNativeAdView(ad, adView)
                listener?.onAdLoaded(adView)
            }
            .withAdListener(object : AdListener() {
                override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                    isLoadingAd = false
                    nativeAd = null
                    val errorMessage = "Native ad failed to load: ${loadAdError.message}"
                    Log.d(TAG, errorMessage)
                    listener?.onAdFailedToLoad(errorMessage)
                }
            })
            .withNativeAdOptions(
                NativeAdOptions.Builder()
                    .setVideoOptions(
                        VideoOptions.Builder()
                            .setStartMuted(true)
                            .build()
                    )
                    .build()
            )
            .build()

        adLoader.loadAd(AdRequest.Builder().build())
    }

    private fun createNativeAdView(): NativeAdView {
        // Inflate your custom native ad layout
        // You need to create a layout file for native ads
        val inflater = LayoutInflater.from(context)
        return inflater.inflate(
            getLayoutResourceId("native_ad_layout"),
            null
        ) as NativeAdView
    }

    private fun populateNativeAdView(nativeAd: NativeAd, adView: NativeAdView) {
        // Set the media view
        adView.mediaView = adView.findViewById(getViewResourceId("ad_media"))

        // Set other view properties
        adView.headlineView = adView.findViewById(getViewResourceId("ad_headline"))
        adView.bodyView = adView.findViewById(getViewResourceId("ad_body"))
        adView.callToActionView = adView.findViewById(getViewResourceId("ad_call_to_action"))
        adView.iconView = adView.findViewById(getViewResourceId("ad_icon"))
        adView.priceView = adView.findViewById(getViewResourceId("ad_price"))
        adView.starRatingView = adView.findViewById(getViewResourceId("ad_stars"))
        adView.storeView = adView.findViewById(getViewResourceId("ad_store"))
        adView.advertiserView = adView.findViewById(getViewResourceId("ad_advertiser"))

        // Set the native ad to the ad view
        adView.setNativeAd(nativeAd)
    }

    fun isAdAvailable(): Boolean {
        return nativeAd != null
    }

    fun getNativeAd(): NativeAd? {
        return nativeAd
    }

    fun destroyNativeAd() {
        nativeAd?.destroy()
        nativeAd = null
    }

    private fun getLayoutResourceId(name: String): Int {
        return context.resources.getIdentifier(name, "layout", context.packageName)
    }

    private fun getViewResourceId(name: String): Int {
        return context.resources.getIdentifier(name, "id", context.packageName)
    }

    companion object {
        private const val TAG = "NativeAdManager"
    }
}