# AdMob 聚合广告集成指南

## 已实现的广告类型

### 1. 开屏广告 (AppOpenAdManager)
- 位置: `AppOpenAdManager.kt`
- 功能: 应用启动时自动显示开屏广告
- 生命周期: 自动管理应用生命周期事件
- 测试ID: `ca-app-pub-3940256099942544/3419835294`

### 2. 插屏广告 (InterstitialAdManager)
- 位置: `InterstitialAdManager.kt`
- 功能: 全屏插页广告，适合场景转换时使用
- 手动控制: 需要手动调用显示
- 测试ID: `ca-app-pub-3940256099942544/1033173712`

### 3. 原生广告 (NativeAdManager)
- 位置: `NativeAdManager.kt`
- 功能: 自定义样式的原生广告
- 需要布局: 需要创建对应的布局文件
- 测试ID: `ca-app-pub-3940256099942544/2247696110`

## 初始化配置

### 1. Application 初始化
AdMob 已在 `RecoverDevApplication` 中自动初始化

### 2. AndroidManifest 配置
已包含必要的权限和 AdMob 应用 ID:
```xml
<meta-data
    android:name="com.google.android.gms.ads.APPLICATION_ID"
    android:value="ca-app-pub-3940256099942544~3347511713" />
```

## 使用方法

### 开屏广告 (自动)
```kotlin
// 自动在应用启动时显示
// 无需额外代码
```

### 插屏广告
```kotlin
val interstitialManager = InterstitialAdManager(context)
interstitialManager.loadAd()

// 显示广告
interstitialManager.showAd(activity) {
    // 广告关闭后的回调
}
```

### 原生广告
```kotlin
val nativeManager = NativeAdManager(context)
nativeManager.loadAd(object : NativeAdManager.NativeAdLoadListener {
    override fun onAdLoaded(adView: NativeAdView) {
        // 显示原生广告
    }
    
    override fun onAdFailedToLoad(error: String) {
        // 处理加载失败
    }
})
```

## 注意事项

1. **替换测试ID**: 上线前务必替换所有测试广告单元ID
2. **合规性**: 确保广告展示符合平台政策
3. **用户体验**: 合理控制广告展示频率
4. **测试设备**: 使用 `AdMobInitializer.setTestDeviceIds()` 设置测试设备

## 生产环境准备

1. 在 AdMob 控制台创建实际广告单元
2. 更新所有广告单元ID
3. 移除测试设备配置
4. 测试真实广告加载和显示

## 扩展建议

1. 添加广告事件统计
2. 实现广告缓存策略
3. 添加广告加载超时处理
4. 实现广告展示频率控制