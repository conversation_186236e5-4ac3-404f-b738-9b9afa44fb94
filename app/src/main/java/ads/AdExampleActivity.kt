package ads

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.google.android.gms.ads.nativead.NativeAdView

/**
 * Example activity demonstrating how to use different AdMob ad types
 * This is for demonstration purposes only - not for immediate use
 */
class AdExampleActivity : ComponentActivity() {

    private lateinit var interstitialAdManager: InterstitialAdManager
    private lateinit var nativeAdManager: NativeAdManager

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize ad managers
        interstitialAdManager = InterstitialAdManager(this)
        nativeAdManager = NativeAdManager(this)

        // Load ads
        interstitialAdManager.loadAd()
        nativeAdManager.loadAd()

        setContent {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Button(
                    onClick = { showInterstitialAd() },
                    modifier = Modifier.padding(8.dp)
                ) {
                    Text("Show Interstitial Ad")
                }

                Button(
                    onClick = { loadNativeAd() },
                    modifier = Modifier.padding(8.dp)
                ) {
                    Text("Load Native Ad")
                }

                Button(
                    onClick = { reloadAllAds() },
                    modifier = Modifier.padding(8.dp)
                ) {
                    Text("Reload All Ads")
                }
            }
        }
    }

    private fun showInterstitialAd() {
        interstitialAdManager.showAd(this) {
            // Ad dismissed callback
            // Reload the ad for next use
            interstitialAdManager.loadAd()
        }
    }

    private fun loadNativeAd() {
        nativeAdManager.loadAd(object : NativeAdManager.NativeAdLoadListener {
            override fun onAdLoaded(adView: NativeAdView) {
                // Handle native ad loaded
                // You can display the adView in your UI
            }

            override fun onAdFailedToLoad(error: String) {
                // Handle ad load failure
            }
        })
    }

    private fun reloadAllAds() {
        interstitialAdManager.loadAd()
        nativeAdManager.loadAd()
    }

    override fun onDestroy() {
        super.onDestroy()
        // Clean up native ad resources
        nativeAdManager.destroyNativeAd()
    }
}